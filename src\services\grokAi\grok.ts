import Groq from "groq-sdk";
const groq = new Groq({ apiKey: process.env.GROQ_API_KEY });

export const suggestImageDetails = async(imageUrl:string) => {
   const chatCompletion = await groq.chat.completions.create({
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "Please provide an array of objects with keys 'title' and 'description' based on the following image."
          },
          {
            "type": "image_url",
            "image_url": {
              "url": imageUrl
            }
          }
        ]
      }
    ],
    "model": "meta-llama/llama-4-scout-17b-16e-instruct",
    "temperature": 1,
    "max_completion_tokens": 500,
    "top_p": 1,
    "stream": false,
    "stop": null
  });

   const message = chatCompletion.choices[0].message.content;
   return message;
}