import { Router } from "express";
import { suggestProductDetails, uploadProduct } from "../controllers/adminController.js";
import upload from "../middlewares/multer.js";
import { updateProduct } from "../controllers/adminController.js";
const adminRouter = Router();
adminRouter.route("/upload-product").post(
    upload.array("images"),
    uploadProduct
);

adminRouter.route("/update-product/:id").patch(updateProduct);
adminRouter.route("/suggest-product-details").get(
    upload.single("image"),
    suggestProductDetails
);
export default adminRouter;