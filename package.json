{"name": "mega-mall", "version": "1.0.0", "description": "This is fully funtioning ecommerce website backend with node and express js.7", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts ", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["node", "js", "express", "js", "mongoose"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"cors": "^2.8.5", "mongodb": "^6.16.0", "mongoose": "^8.15.0"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/node": "^22.15.19", "dotenv": "^16.5.0", "eslint": "^9.27.0", "express": "^5.1.0", "install": "^0.13.0", "nodemon": "^3.1.10", "npm": "^10.9.2", "prettier": "^3.5.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}