{"name": "mega-mall", "version": "1.0.0", "description": "This is fully funtioning ecommerce website backend with node and express js.7", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsc --watch & nodemon dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["node", "js", "express", "js", "mongoose"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"cors": "^2.8.5", "express": "^5.1.0", "mongoose": "^8.15.0", "typescript": "^5.8.3"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2"}}