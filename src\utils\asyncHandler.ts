import {Request, Response , NextFunction } from "express"
import { apiResponse } from "./apiResponse";

const asynHandler = (fn: Function) => async(req: Request, res: Response, next: NextFunction) => {

    // interface CustomError extends Error {
    //     status?: number
    // }
    try {
        await fn(req,res,next);
    } catch (error: unknown) {
        if(error instanceof Error){
            res.status(500).json(
                apiResponse({
                    success: false,
                    status: 500,
                    message: error.message || "Something went wrong",
                    data: []
                })
            );
            return next(error);
        }
    }
}