"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
// @ts-ignore TS6133
const vitest_1 = require("vitest");
const z = __importStar(require("zod/v3"));
const util_js_1 = require("../helpers/util.js");
(0, vitest_1.test)("first party switch", () => {
    const myType = z.string();
    const def = myType._def;
    switch (def.typeName) {
        case z.ZodFirstPartyTypeKind.ZodString:
            break;
        case z.ZodFirstPartyTypeKind.ZodNumber:
            break;
        case z.ZodFirstPartyTypeKind.ZodNaN:
            break;
        case z.ZodFirstPartyTypeKind.ZodBigInt:
            break;
        case z.ZodFirstPartyTypeKind.ZodBoolean:
            break;
        case z.ZodFirstPartyTypeKind.ZodDate:
            break;
        case z.ZodFirstPartyTypeKind.ZodUndefined:
            break;
        case z.ZodFirstPartyTypeKind.ZodNull:
            break;
        case z.ZodFirstPartyTypeKind.ZodAny:
            break;
        case z.ZodFirstPartyTypeKind.ZodUnknown:
            break;
        case z.ZodFirstPartyTypeKind.ZodNever:
            break;
        case z.ZodFirstPartyTypeKind.ZodVoid:
            break;
        case z.ZodFirstPartyTypeKind.ZodArray:
            break;
        case z.ZodFirstPartyTypeKind.ZodObject:
            break;
        case z.ZodFirstPartyTypeKind.ZodUnion:
            break;
        case z.ZodFirstPartyTypeKind.ZodDiscriminatedUnion:
            break;
        case z.ZodFirstPartyTypeKind.ZodIntersection:
            break;
        case z.ZodFirstPartyTypeKind.ZodTuple:
            break;
        case z.ZodFirstPartyTypeKind.ZodRecord:
            break;
        case z.ZodFirstPartyTypeKind.ZodMap:
            break;
        case z.ZodFirstPartyTypeKind.ZodSet:
            break;
        case z.ZodFirstPartyTypeKind.ZodFunction:
            break;
        case z.ZodFirstPartyTypeKind.ZodLazy:
            break;
        case z.ZodFirstPartyTypeKind.ZodLiteral:
            break;
        case z.ZodFirstPartyTypeKind.ZodEnum:
            break;
        case z.ZodFirstPartyTypeKind.ZodEffects:
            break;
        case z.ZodFirstPartyTypeKind.ZodNativeEnum:
            break;
        case z.ZodFirstPartyTypeKind.ZodOptional:
            break;
        case z.ZodFirstPartyTypeKind.ZodNullable:
            break;
        case z.ZodFirstPartyTypeKind.ZodDefault:
            break;
        case z.ZodFirstPartyTypeKind.ZodCatch:
            break;
        case z.ZodFirstPartyTypeKind.ZodPromise:
            break;
        case z.ZodFirstPartyTypeKind.ZodBranded:
            break;
        case z.ZodFirstPartyTypeKind.ZodPipeline:
            break;
        case z.ZodFirstPartyTypeKind.ZodSymbol:
            break;
        case z.ZodFirstPartyTypeKind.ZodReadonly:
            break;
        default:
            util_js_1.util.assertNever(def);
    }
});
