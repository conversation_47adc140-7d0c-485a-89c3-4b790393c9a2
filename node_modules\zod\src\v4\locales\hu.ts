import type { $ZodStringFormats } from "../core/checks.js";
import type * as errors from "../core/errors.js";
import * as util from "../core/util.js";

const Sizable: Record<string, { unit: string; verb: string }> = {
  string: { unit: "karakter", verb: "legyen" },
  file: { unit: "byte", verb: "legyen" },
  array: { unit: "elem", verb: "legyen" },
  set: { unit: "elem", verb: "legyen" },
};

function getSizing(origin: string): { unit: string; verb: string } | null {
  return Sizable[origin] ?? null;
}

export const parsedType = (data: any): string => {
  const t = typeof data;

  switch (t) {
    case "number": {
      return Number.isNaN(data) ? "NaN" : "szám";
    }
    case "object": {
      if (Array.isArray(data)) {
        return "tömb";
      }
      if (data === null) {
        return "null";
      }

      if (Object.getPrototypeOf(data) !== Object.prototype && data.constructor) {
        return data.constructor.name;
      }
    }
  }
  return t;
};

const Nouns: {
  [k in $ZodStringFormats | (string & {})]?: string;
} = {
  regex: "bemenet",
  email: "email cím",
  url: "URL",
  emoji: "emoji",
  uuid: "UUID",
  uuidv4: "UUIDv4",
  uuidv6: "UUIDv6",
  nanoid: "nanoid",
  guid: "GUID",
  cuid: "cuid",
  cuid2: "cuid2",
  ulid: "ULID",
  xid: "XID",
  ksuid: "KSUID",
  datetime: "ISO időbélyeg",
  date: "ISO dátum",
  time: "ISO idő",
  duration: "ISO időintervallum",
  ipv4: "IPv4 cím",
  ipv6: "IPv6 cím",
  cidrv4: "IPv4 tartomány",
  cidrv6: "IPv6 tartomány",
  base64: "base64-kódolt string",
  base64url: "base64url-kódolt string",
  json_string: "JSON string",
  e164: "E.164 szám",
  jwt: "JWT",
  template_literal: "bemenet",
};

const error: errors.$ZodErrorMap = (issue) => {
  switch (issue.code) {
    case "invalid_type":
      return `Érvénytelen bemenet: a várt érték ${issue.expected}, a kapott érték ${parsedType(issue.input)}`;
    // return `Invalid input: expected ${issue.expected}, received ${util.getParsedType(issue.input)}`;
    case "invalid_value":
      if (issue.values.length === 1)
        return `Érvénytelen bemenet: a várt érték ${util.stringifyPrimitive(issue.values[0])}`;
      return `Érvénytelen opció: valamelyik érték várt ${util.joinValues(issue.values, "|")}`;
    case "too_big": {
      const adj = issue.inclusive ? "<=" : "<";
      const sizing = getSizing(issue.origin);
      if (sizing)
        return `Túl nagy: ${issue.origin ?? "érték"} mérete túl nagy ${adj}${issue.maximum.toString()} ${sizing.unit ?? "elem"}`;
      return `Túl nagy: a bemeneti érték ${issue.origin ?? "érték"} túl nagy: ${adj}${issue.maximum.toString()}`;
    }
    case "too_small": {
      const adj = issue.inclusive ? ">=" : ">";
      const sizing = getSizing(issue.origin);
      if (sizing) {
        return `Túl kicsi: a bemeneti érték ${issue.origin} mérete túl kicsi ${adj}${issue.minimum.toString()} ${sizing.unit}`;
      }

      return `Túl kicsi: a bemeneti érték ${issue.origin} túl kicsi ${adj}${issue.minimum.toString()}`;
    }
    case "invalid_format": {
      const _issue = issue as errors.$ZodStringFormatIssues;
      if (_issue.format === "starts_with") return `Érvénytelen string: "${_issue.prefix}" értékkel kell kezdődnie`;
      if (_issue.format === "ends_with") return `Érvénytelen string: "${_issue.suffix}" értékkel kell végződnie`;
      if (_issue.format === "includes") return `Érvénytelen string: "${_issue.includes}" értéket kell tartalmaznia`;
      if (_issue.format === "regex") return `Érvénytelen string: ${_issue.pattern} mintának kell megfelelnie`;
      return `Érvénytelen ${Nouns[_issue.format] ?? issue.format}`;
    }
    case "not_multiple_of":
      return `Érvénytelen szám: ${issue.divisor} többszörösének kell lennie`;
    case "unrecognized_keys":
      return `Ismeretlen kulcs${issue.keys.length > 1 ? "s" : ""}: ${util.joinValues(issue.keys, ", ")}`;
    case "invalid_key":
      return `Érvénytelen kulcs ${issue.origin}`;
    case "invalid_union":
      return "Érvénytelen bemenet";
    case "invalid_element":
      return `Érvénytelen érték: ${issue.origin}`;
    default:
      return `Érvénytelen bemenet`;
  }
};

export { error };

export default function (): { localeError: errors.$ZodErrorMap } {
  return {
    localeError: error,
  };
}
