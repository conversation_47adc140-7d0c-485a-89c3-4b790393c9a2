import mongoose from "mongoose";

const dbConnection = async (): Promise<void> => {
  try {
    const MONGO_URI = process.env.MONGO_URI || "mongodb://localhost:27017";
    const MONGO_NAME = process.env.MONGO_NAME || "mega-mall";
    await mongoose.connect(`${MONGO_URI}/${MONGO_NAME}`);
    console.log("DB Connected");
  } catch (error: unknown) {
    if(error instanceof Error){
      console.log(error.message);
    }
  }
};

export default dbConnection;
