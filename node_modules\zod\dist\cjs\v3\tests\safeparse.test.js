"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
// @ts-ignore TS6133
const vitest_1 = require("vitest");
const z = __importStar(require("zod/v3"));
const stringSchema = z.string();
(0, vitest_1.test)("safeparse fail", () => {
    const safe = stringSchema.safeParse(12);
    (0, vitest_1.expect)(safe.success).toEqual(false);
    (0, vitest_1.expect)(safe.error).toBeInstanceOf(z.ZodError);
});
(0, vitest_1.test)("safeparse pass", () => {
    const safe = stringSchema.safeParse("12");
    (0, vitest_1.expect)(safe.success).toEqual(true);
    (0, vitest_1.expect)(safe.data).toEqual("12");
});
(0, vitest_1.test)("safeparse unexpected error", () => {
    (0, vitest_1.expect)(() => stringSchema
        .refine((data) => {
        throw new Error(data);
    })
        .safeParse("12")).toThrow();
});
