"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const z = __importStar(require("zod/v3"));
(0, vitest_1.test)("test", () => {
    (0, vitest_1.expect)(true).toBe(true);
});
(0, vitest_1.test)("test2", () => {
    (0, vitest_1.expect)(() => z.string().parse(234)).toThrowErrorMatchingInlineSnapshot(`
    [ZodError: [
      {
        "code": "invalid_type",
        "expected": "string",
        "received": "number",
        "path": [],
        "message": "Expected string, received number"
      }
    ]]
  `);
});
(0, vitest_1.test)("async validation", async () => {
    const testTuple = z
        .tuple([z.string().refine(async () => true), z.number().refine(async () => true)])
        .refine(async () => true);
    (0, vitest_1.expectTypeOf)().toEqualTypeOf();
    const val = await testTuple.parseAsync(["asdf", 1234]);
    (0, vitest_1.expect)(val).toEqual(val);
    const r1 = await testTuple.safeParseAsync(["asdf", "asdf"]);
    (0, vitest_1.expect)(r1.success).toEqual(false);
    (0, vitest_1.expect)(r1.error).toMatchInlineSnapshot(`
    [ZodError: [
      {
        "code": "invalid_type",
        "expected": "number",
        "received": "string",
        "path": [
          1
        ],
        "message": "Expected number, received string"
      }
    ]]
  `);
});
