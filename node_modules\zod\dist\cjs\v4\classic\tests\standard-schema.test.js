"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const z = __importStar(require("zod/v4"));
(0, vitest_1.test)("length checks", async () => {
    const schema = z.string();
    const result = await schema["~standard"].validate(12);
    (0, vitest_1.expect)(result).toMatchInlineSnapshot(`
    {
      "issues": [
        {
          "code": "invalid_type",
          "expected": "string",
          "message": "Invalid input: expected string, received number",
          "path": [],
        },
      ],
    }
  `);
});
(0, vitest_1.test)("length checks", async () => {
    const schema = z.string();
    const result = await schema["~standard"].validate("asdf");
    (0, vitest_1.expect)(result).toMatchInlineSnapshot(`
    {
      "value": "asdf",
    }
  `);
});
(0, vitest_1.test)("length checks", async () => {
    const schema = z.string().refine(async (val) => val.length > 5);
    const result = await schema["~standard"].validate(12);
    (0, vitest_1.expect)(result).toMatchInlineSnapshot(`
    {
      "issues": [
        {
          "code": "invalid_type",
          "expected": "string",
          "message": "Invalid input: expected string, received number",
          "path": [],
        },
      ],
    }
  `);
});
(0, vitest_1.test)("length checks", async () => {
    const schema = z.string().refine(async (val) => val.length > 5);
    const result = await schema["~standard"].validate("234134134");
    (0, vitest_1.expect)(result).toMatchInlineSnapshot(`
    {
      "value": "234134134",
    }
  `);
});
