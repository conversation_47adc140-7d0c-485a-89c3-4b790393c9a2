export * as core from "zod/v4/core";
export * from "./parse.js";
export * from "./schemas.js";
export * from "./checks.js";

export type { infer, output, input } from "zod/v4/core";
export {
  globalRegistry,
  registry,
  config,
  $output,
  $input,
  $brand,
  function,
  clone,
  regexes,
  treeifyError,
  prettifyError,
  formatError,
  flattenError,
  toJSONSchema,
  locales,
} from "zod/v4/core";

/** A special constant with type `never` */
// export const NEVER = {} as never;
