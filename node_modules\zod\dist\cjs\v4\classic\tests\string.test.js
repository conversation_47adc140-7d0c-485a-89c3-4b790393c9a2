"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_crypto_1 = require("node:crypto");
const vitest_1 = require("vitest");
const z = __importStar(require("zod/v4"));
const minFive = z.string().min(5, "min5");
const maxFive = z.string().max(5, "max5");
const justFive = z.string().length(5);
const nonempty = z.string().min(1, "nonempty");
const includes = z.string().includes("includes");
const includesFromIndex2 = z.string().includes("includes", { position: 2 });
const startsWith = z.string().startsWith("startsWith");
const endsWith = z.string().endsWith("endsWith");
(0, vitest_1.test)("length checks", () => {
    minFive.parse("12345");
    minFive.parse("123456");
    maxFive.parse("12345");
    maxFive.parse("1234");
    nonempty.parse("1");
    justFive.parse("12345");
    (0, vitest_1.expect)(() => minFive.parse("1234")).toThrow();
    (0, vitest_1.expect)(() => maxFive.parse("123456")).toThrow();
    (0, vitest_1.expect)(() => nonempty.parse("")).toThrow();
    (0, vitest_1.expect)(() => justFive.parse("1234")).toThrow();
    (0, vitest_1.expect)(() => justFive.parse("123456")).toThrow();
});
(0, vitest_1.test)("includes", () => {
    includes.parse("XincludesXX");
    includesFromIndex2.parse("XXXincludesXX");
    (0, vitest_1.expect)(() => includes.parse("XincludeXX")).toThrow();
    (0, vitest_1.expect)(() => includesFromIndex2.parse("XincludesXX")).toThrow();
});
(0, vitest_1.test)("startswith/endswith", () => {
    startsWith.parse("startsWithX");
    endsWith.parse("XendsWith");
    (0, vitest_1.expect)(() => startsWith.parse("x")).toThrow();
    (0, vitest_1.expect)(() => endsWith.parse("x")).toThrow();
});
(0, vitest_1.test)("email validations", () => {
    const validEmails = [
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `common'<EMAIL>`,
        `<EMAIL>`,
    ];
    const invalidEmails = [
        // no "printable characters"
        // `<EMAIL>`,
        // `mailhost!<EMAIL>`,
        // `test/<EMAIL>`,
        // double @
        `francois@@etu.inp-n7.fr`,
        // do not support quotes
        `"email"@domain.com`,
        `"e asdf sadf ?<>ail"@domain.com`,
        `" "@example.org`,
        `"john..doe"@example.org`,
        `"very.(),:;<>[]\".VERY.\"very@\\ \"very\".unusual"@strange.example.com`,
        // do not support comma
        `a,<EMAIL>`,
        // do not support IPv4
        `email@***************`,
        `email@[***************]`,
        `postmaster@***************`,
        `user@[**************]`,
        `ipv4@[*************]`,
        `valid@[*************]`,
        `valid@[***************]`,
        `valid@[**********]`,
        `valid@[**********]`,
        // do not support ipv6
        `hgrebert0@[IPv6:4dc8:ac7:ce79:8878:1290:6098:5c50:1f25]`,
        `bshapiro4@[IPv6:3669:c709:e981:4884:59a3:75d1:166b:9ae]`,
        `jsmith@[IPv6:2001:db8::1]`,
        `postmaster@[IPv6:2001:0db8:85a3:0000:0000:8a2e:0370:7334]`,
        `postmaster@[IPv6:2001:0db8:85a3:0000:0000:8a2e:0370:***********]`,
        // microsoft test cases
        `plainaddress`,
        `#@%^%#$@#$@#.com`,
        `@domain.com`,
        `Joe Smith &lt;<EMAIL>&gt;`,
        `email.domain.com`,
        `email@<EMAIL>`,
        `.<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `あいうえお@domain.com`,
        `<EMAIL> (Joe Smith)`,
        `email@domain`,
        `<EMAIL>`,
        `email@111.222.333.44444`,
        `<EMAIL>`,
        `Abc.example.com`,
        `A@b@<EMAIL>`,
        `<EMAIL>`,
        `a"b(c)d,e:f;g<h>i[j\k]<EMAIL>`,
        `just"not"<EMAIL>`,
        `this is"not\<EMAIL>`,
        `this\ still\"not\\<EMAIL>`,
        // random
        `i_like_underscore@but_its_not_allowed_in_this_part.example.com`,
        `QA[icon]CHOCOLATE[icon]@test.com`,
        `<EMAIL>`,
        `<EMAIL>-`,
        `a.b@c.d`,
        `invalid@[1.1.1.-1]`,
        `invalid@[**************.55]`,
        `temp@[192.168.1]`,
        `temp@[9.18.122.]`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `<EMAIL>`,
        `invalid@[256.2.2.48]`,
        `invalid@[256.2.2.48]`,
        `invalid@[999.465.265.1]`,
        `jkibbey4@[IPv6:82c4:19a8::70a9:2aac:557::ea69:d985:28d]`,
        `mlivesay3@[9952:143f:b4df:2179:49a1:5e82:b92e:6b6]`,
        `gbacher0@[IPv6:bc37:4d3f:5048:2e26:37cc:248e:df8e:2f7f:af]`,
        `invalid@[IPv6:5348:4ed3:5d38:67fb:e9b:acd2:c13:192.168.256.1]`,
        `test@.com`,
        `aaaaaaaaaaaaaaalongemailthatcausesregexDoSvulnerability@test.c`,
    ];
    const emailSchema = z.string().email();
    (0, vitest_1.expect)(validEmails.every((email) => {
        return emailSchema.safeParse(email).success;
    })).toBe(true);
    (0, vitest_1.expect)(invalidEmails.every((email) => {
        return emailSchema.safeParse(email).success === false;
    })).toBe(true);
});
(0, vitest_1.test)("base64 validations", () => {
    const validBase64Strings = [
        "SGVsbG8gV29ybGQ=", // "Hello World"
        "VGhpcyBpcyBhbiBlbmNvZGVkIHN0cmluZw==", // "This is an encoded string"
        "TWFueSBoYW5kcyBtYWtlIGxpZ2h0IHdvcms=", // "Many hands make light work"
        "UGF0aWVuY2UgaXMgdGhlIGtleSB0byBzdWNjZXNz", // "Patience is the key to success"
        "QmFzZTY0IGVuY29kaW5nIGlzIGZ1bg==", // "Base64 encoding is fun"
        "MTIzNDU2Nzg5MA==", // "1234567890"
        "YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXo=", // "abcdefghijklmnopqrstuvwxyz"
        "QUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVo=", // "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        "ISIkJSMmJyonKCk=", // "!\"#$%&'()*"
        "", // Empty string is technically a valid base64
    ];
    for (const str of validBase64Strings) {
        (0, vitest_1.expect)(str + z.string().base64().safeParse(str).success).toBe(`${str}true`);
    }
    const invalidBase64Strings = [
        "12345", // Not padded correctly, not a multiple of 4 characters
        "SGVsbG8gV29ybGQ", // Missing padding
        "VGhpcyBpcyBhbiBlbmNvZGVkIHN0cmluZw", // Missing padding
        "!UGF0aWVuY2UgaXMgdGhlIGtleSB0byBzdWNjZXNz", // Invalid character '!'
        "?QmFzZTY0IGVuY29kaW5nIGlzIGZ1bg==", // Invalid character '?'
        ".MTIzND2Nzg5MC4=", // Invalid character '.'
        "QUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVo", // Missing padding
    ];
    for (const str of invalidBase64Strings) {
        (0, vitest_1.expect)(str + z.string().base64().safeParse(str).success).toBe(`${str}false`);
    }
});
(0, vitest_1.test)("base64url validations", () => {
    const base64url = z.string().base64url();
    const validBase64URLStrings = [
        "SGVsbG8gV29ybGQ", // "Hello World"
        "VGhpcyBpcyBhbiBlbmNvZGVkIHN0cmluZw", // "This is an encoded string"
        "TWFueSBoYW5kcyBtYWtlIGxpZ2h0IHdvcms", // "Many hands make light work"
        "UGF0aWVuY2UgaXMgdGhlIGtleSB0byBzdWNjZXNz", // "Patience is the key to success"
        "QmFzZTY0IGVuY29kaW5nIGlzIGZ1bg", // "Base64 encoding is fun"
        "MTIzNDU2Nzg5MA", // "1234567890"
        "YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXo", // "abcdefghijklmnopqrstuvwxyz"
        "QUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVo", // "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        "ISIkJSMmJyonKCk", // "!\"#$%&'()*"
        "", // Empty string is technically valid base64url
        "w7_Dv8O-w74K", // ÿÿþþ
        "123456",
    ];
    for (const str of validBase64URLStrings) {
        (0, vitest_1.expect)(str + base64url.safeParse(str).success).toBe(`${str}true`);
    }
    const invalidBase64URLStrings = [
        "w7/Dv8O+w74K", // Has + and / characters (is base64)
        "12345", // Invalid length (not a multiple of 4 characters when adding allowed number of padding characters)
        "12345===", // Not padded correctly
        "!UGF0aWVuY2UgaXMgdGhlIGtleSB0byBzdWNjZXNz", // Invalid character '!'
        "?QmFzZTY0IGVuY29kaW5nIGlzIGZ1bg==", // Invalid character '?'
        ".MTIzND2Nzg5MC4=", // Invalid character '.'
        // disallow valid padding
        "SGVsbG8gV29ybGQ=", // "Hello World" with padding
        "VGhpcyBpcyBhbiBlbmNvZGVkIHN0cmluZw==", // "This is an encoded string" with padding
        "TWFueSBoYW5kcyBtYWtlIGxpZ2h0IHdvcms=", // "Many hands make light work" with padding
        "QmFzZTY0IGVuY29kaW5nIGlzIGZ1bg==", // "Base64 encoding is fun" with padding
        "MTIzNDU2Nzg5MA==", // "1234567890" with padding
        "YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXo=", // "abcdefghijklmnopqrstuvwxyz with padding"
        "QUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVo=", // "ABCDEFGHIJKLMNOPQRSTUVWXYZ" with padding
        "ISIkJSMmJyonKCk=", // "!\"#$%&'()*" with padding
    ];
    for (const str of invalidBase64URLStrings) {
        (0, vitest_1.expect)(str + base64url.safeParse(str).success).toBe(`${str}false`);
    }
});
(0, vitest_1.test)("big base64 and base64url", () => {
    const bigbase64 = (0, node_crypto_1.randomBytes)(1024 * 1024 * 10).toString("base64");
    z.base64().parse(bigbase64);
    const bigbase64url = (0, node_crypto_1.randomBytes)(1024 * 1024 * 10).toString("base64url");
    z.base64url().parse(bigbase64url);
});
function makeJwt(header, payload) {
    const headerBase64 = Buffer.from(JSON.stringify(header)).toString("base64url");
    const payloadBase64 = Buffer.from(JSON.stringify(payload)).toString("base64url");
    const signature = "signature"; // Placeholder for the signature
    return `${headerBase64}.${payloadBase64}.${signature}`;
}
(0, vitest_1.test)("jwt token", () => {
    const jwt = z.string().jwt();
    (0, vitest_1.expect)(() => jwt.parse("invalid")).toThrow();
    (0, vitest_1.expect)(() => jwt.parse("invalid.invalid")).toThrow();
    (0, vitest_1.expect)(() => jwt.parse("invalid.invalid.invalid")).toThrow();
    // Valid JWTs
    const es256jwt = z.string().jwt({ alg: "ES256" });
    const d1 = makeJwt({ typ: "JWT", alg: "ES256" }, {});
    jwt.parse(d1);
    es256jwt.parse(d1);
    // Invalid header
    const d2 = makeJwt({}, {});
    (0, vitest_1.expect)(() => jwt.parse(d2)).toThrow();
    // Wrong algorithm
    const d3 = makeJwt({ typ: "JWT", alg: "RS256" }, {});
    (0, vitest_1.expect)(() => es256jwt.parse(d3)).toThrow();
    // missing typ is fine
    const d4 = makeJwt({ alg: "HS256" }, {});
    jwt.parse(d4);
    // type isn't JWT
    const d5 = makeJwt({ typ: "SUP", alg: "HS256" }, { foo: "bar" });
    (0, vitest_1.expect)(() => jwt.parse(d5)).toThrow();
    // const ONE_PART = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9";
    // const NOT_BASE64 =
    //   "headerIsNotBase64Encoded.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.RRi1X2IlXd5rZa9Mf_0VUOf-RxOzAhbB4tgViUGamWE";
    // const NO_TYP =
    //   "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.GuoUe6tw79bJlbU1HU0ADX0pr0u2kf3r_4OdrDufSfQ";
    // const TYP_NOT_JWT =
    //   "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpUVyJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.RRi1X2IlXd5rZa9Mf_0VUOf-RxOzAhbB4tgViUGamWE";
    // const GOOD_JWT_HS256 =
    //   "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
    // const GOOD_JWT_ES256 =
    //   "***********************************************************************************************************************************************************************************************************************";
    // const jwtSchema = z.string().jwt();
    // expect(() => jwtSchema.parse(ONE_PART)).toThrow();
    // expect(() => jwtSchema.parse(NOT_BASE64)).toThrow();
    // expect(() => jwtSchema.parse(TYP_NOT_JWT)).toThrow();
    // expect(() => jwtSchema.parse(TYP_NOT_JWT)).toThrow();
    // expect(() => jwtSchema.parse(TYP_NOT_JWT)).toThrow();
    // expect(() => z.string().jwt({ alg: "ES256" }).parse(GOOD_JWT_HS256)).toThrow();
    // expect(() => z.string().jwt({ alg: "HS256" }).parse(GOOD_JWT_ES256)).toThrow();
    // //Success
    // jwtSchema.parse(NO_TYP); // allow no typ
    // expect(() => jwtSchema.parse(GOOD_JWT_HS256)).not.toThrow();
    // expect(() => jwtSchema.parse(GOOD_JWT_ES256)).not.toThrow();
    // expect(() => z.string().jwt({ alg: "HS256" }).parse(GOOD_JWT_HS256)).not.toThrow();
    // expect(() => z.string().jwt({ alg: "ES256" }).parse(GOOD_JWT_ES256)).not.toThrow();
});
(0, vitest_1.test)("url validations", () => {
    const url = z.string().url();
    url.parse("http://google.com");
    url.parse("https://google.com/asdf?asdf=ljk3lk4&asdf=234#asdf");
    url.parse("https://anonymous:<EMAIL>/en-US/docs/Web/API/URL/password");
    url.parse("https://localhost");
    url.parse("https://my.local");
    url.parse("http://aslkfjdalsdfkjaf");
    url.parse("http://localhost");
    (0, vitest_1.expect)(() => url.parse("asdf")).toThrow();
    (0, vitest_1.expect)(() => url.parse("http:.......///broken.com")).toThrow();
    (0, vitest_1.expect)(() => url.parse("c:")).toThrow();
    (0, vitest_1.expect)(() => url.parse("WWW:WWW.COM")).toThrow();
    (0, vitest_1.expect)(() => url.parse("https:/")).toThrow();
    (0, vitest_1.expect)(() => url.parse("<EMAIL>")).toThrow();
});
(0, vitest_1.test)("url error overrides", () => {
    try {
        z.string().url().parse("https");
    }
    catch (err) {
        (0, vitest_1.expect)(err.issues[0].message).toEqual("Invalid URL");
    }
    try {
        z.string().url("badurl").parse("https");
    }
    catch (err) {
        (0, vitest_1.expect)(err.issues[0].message).toEqual("badurl");
    }
    try {
        z.string().url({ message: "badurl" }).parse("https");
    }
    catch (err) {
        (0, vitest_1.expect)(err.issues[0].message).toEqual("badurl");
    }
});
(0, vitest_1.test)("emoji validations", () => {
    const emoji = z.string().emoji();
    emoji.parse("👋👋👋👋");
    emoji.parse("🍺👩‍🚀🫡");
    emoji.parse("💚💙💜💛❤️");
    emoji.parse("🐛🗝🐏🍡🎦🚢🏨💫🎌☘🗡😹🔒🎬➡️🍹🗂🚨⚜🕑〽️🚦🌊🍴💍🍌💰😳🌺🍃");
    emoji.parse("🇹🇷🤽🏿‍♂️");
    emoji.parse("😀😁😂🤣😃😄😅😆😉😊😋😎😍😘🥰😗😙😚☺️☺🙂🤗🤩🤔🤨😐😑😶🙄😏😣😥😮🤐😯😪😫😴😌😛😜😝🤤😒😓😔😕🙃🤑😲☹️☹🙁😖😞😟😤😢😭😦😧😨😩🤯😬😰😱🥵🥶😳🤪😵😡😠🤬😷🤒🤕🤢🤮🤧😇🤠🥳🥴🥺🤥🤫🤭🧐🤓😈👿🤡👹👺💀☠️☠👻👽👾🤖💩😺😸😹😻😼😽🙀😿😾🙈🙉🙊🏻🏼🏽🏾🏿👶👶🏻👶🏼👶🏽👶🏾👶🏿🧒🧒🏻🧒🏼🧒🏽🧒🏾🧒🏿👦👦🏻👦🏼👦🏽👦🏾👦🏿👧👧🏻👧🏼👧🏽👧🏾👧🏿🧑🧑🏻🧑🏼🧑🏽🧑🏾🧑🏿👨👨🏻👨🏼👨🏽👨🏾👨🏿👩👩🏻👩🏼👩🏽👩🏾👩🏿🧓🧓🏻🧓🏼🧓🏽🧓🏾🧓🏿👴👴🏻👴🏼👴🏽👴🏾👴🏿👵👵🏻👵🏼👵🏽👵🏾👵🏿👨‍⚕️👨‍⚕👨🏻‍⚕️👨🏻‍⚕👨🏼‍⚕️👨🏼‍⚕👨🏽‍⚕️👨🏽‍⚕👨🏾‍⚕️👨🏾‍⚕👨🏿‍⚕️👨🏿‍⚕👩‍⚕️👩‍⚕👩🏻‍⚕️👩🏻‍⚕👩🏼‍⚕️👩🏼‍⚕👩🏽‍⚕️👩🏽‍⚕👩🏾‍⚕️👩🏾‍⚕👩🏿‍⚕️👩🏿‍⚕👨‍🎓👨🏻‍🎓👨🏼‍🎓👨🏽‍🎓👨🏾‍🎓👨🏿‍🎓👩‍🎓👩🏻‍🎓👩🏼‍🎓👩🏽‍🎓👩🏾‍🎓👩🏿‍🎓👨‍🏫👨🏻‍🏫👨🏼‍🏫👨🏽‍🏫👨🏾‍🏫👨🏿‍🏫👩‍🏫👩🏻‍🏫👩🏼‍🏫👩🏽‍🏫👩🏾‍🏫👩🏿‍🏫👨‍⚖️👨‍⚖👨🏻‍⚖️👨🏻‍⚖👨🏼‍⚖️👨🏼‍⚖👨🏽‍⚖️👨🏽‍⚖👨🏾‍⚖️👨🏾‍⚖👨🏿‍⚖️👨🏿‍⚖👩‍⚖️👩‍⚖👩🏻‍⚖️👩🏻‍⚖👩🏼‍⚖️👩🏼‍⚖👩🏽‍⚖️👩🏽‍⚖👩🏾‍⚖️👩🏾‍⚖👩🏿‍⚖️👩🏿‍⚖👨‍🌾👨🏻‍🌾👨🏼‍🌾👨🏽‍🌾👨🏾‍🌾👨🏿‍🌾👩‍🌾👩🏻‍🌾👩🏼‍🌾👩🏽‍🌾👩🏾‍🌾👩🏿‍🌾👨‍🍳👨🏻‍🍳👨🏼‍🍳👨🏽‍🍳👨🏾‍🍳👨🏿‍🍳👩‍🍳👩🏻‍🍳👩🏼‍🍳👩🏽‍🍳👩🏾‍🍳👩🏿‍🍳👨‍🔧👨🏻‍🔧👨🏼‍🔧👨🏽‍🔧👨🏾‍🔧👨🏿‍🔧👩‍🔧👩🏻‍🔧👩🏼‍🔧👩🏽‍🔧👩🏾‍🔧👩🏿‍🔧👨‍🏭👨🏻‍🏭👨🏼‍🏭👨🏽‍🏭👨🏾‍🏭👨🏿‍🏭👩‍🏭👩🏻‍🏭👩🏼‍🏭👩🏽‍🏭👩🏾‍🏭👩🏿‍🏭👨‍💼👨🏻‍💼👨🏼‍💼👨🏽‍💼👨🏾‍💼👨🏿‍💼👩‍💼👩🏻‍💼👩🏼‍💼👩🏽‍💼👩🏾‍💼👩🏿‍💼👨‍🔬👨🏻‍🔬👨🏼‍🔬👨🏽‍🔬👨🏾‍🔬👨🏿‍🔬👩‍🔬👩🏻‍🔬👩🏼‍🔬👩🏽‍🔬👩🏾‍🔬👩🏿‍🔬👨‍💻👨🏻‍💻👨🏼‍💻👨🏽‍💻👨🏾‍💻👨🏿‍💻👩‍💻👩🏻‍💻👩🏼‍💻👩🏽‍💻👩🏾‍💻👩🏿‍💻👨‍🎤👨🏻‍🎤👨🏼‍🎤👨🏽‍🎤👨🏾‍🎤👨🏿‍🎤👩‍🎤👩🏻‍🎤👩🏼‍🎤👩🏽‍🎤👩🏾‍🎤👩🏿‍🎤👨‍🎨👨🏻‍🎨👨🏼‍🎨👨🏽‍🎨👨🏾‍🎨👨🏿‍🎨👩‍🎨👩🏻‍🎨👩🏼‍🎨👩🏽‍🎨👩🏾‍🎨👩🏿‍🎨👨‍✈️👨‍✈👨🏻‍✈️👨🏻‍✈👨🏼‍✈️👨🏼‍✈👨🏽‍✈️👨🏽‍✈👨🏾‍✈️👨🏾‍✈👨🏿‍✈️👨🏿‍✈👩‍✈️👩‍✈👩🏻‍✈️👩🏻‍✈👩🏼‍✈️👩🏼‍✈👩🏽‍✈️👩🏽‍✈👩🏾‍✈️👩🏾‍✈👩🏿‍✈️👩🏿‍✈👨‍🚀👨🏻‍🚀👨🏼‍🚀👨🏽‍🚀👨🏾‍🚀👨🏿‍🚀👩‍🚀👩🏻‍🚀👩🏼‍🚀👩🏽‍🚀👩🏾‍🚀👩🏿‍🚀👨‍🚒👨🏻‍🚒👨🏼‍🚒👨🏽‍🚒👨🏾‍🚒👨🏿‍🚒👩‍🚒👩🏻‍🚒👩🏼‍🚒👩🏽‍🚒👩🏾‍🚒👩🏿‍🚒👮👮🏻👮🏼👮🏽👮🏾👮🏿👮‍♂️👮‍♂👮🏻‍♂️👮🏻‍♂👮🏼‍♂️👮🏼‍♂👮🏽‍♂️👮🏽‍♂👮🏾‍♂️👮🏾‍♂👮🏿‍♂️👮🏿‍♂👮‍♀️👮‍♀👮🏻‍♀️👮🏻‍♀👮🏼‍♀️👮🏼‍♀👮🏽‍♀️👮🏽‍♀👮🏾‍♀️👮🏾‍♀👮🏿‍♀️👮🏿‍♀🕵️🕵🕵🏻🕵🏼🕵🏽🕵🏾🕵🏿🕵️‍♂️🕵‍♂️🕵️‍♂🕵‍♂🕵🏻‍♂️🕵🏻‍♂🕵🏼‍♂️🕵🏼‍♂🕵🏽‍♂️🕵🏽‍♂🕵🏾‍♂️🕵🏾‍♂🕵🏿‍♂️🕵🏿‍♂🕵️‍♀️🕵‍♀️🕵️‍♀🕵‍♀🕵🏻‍♀️🕵🏻‍♀🕵🏼‍♀️🕵🏼‍♀🕵🏽‍♀️🕵🏽‍♀🕵🏾‍♀️🕵🏾‍♀🕵🏿‍♀️🕵🏿‍♀💂💂🏻💂🏼💂🏽💂🏾💂🏿💂‍♂️💂‍♂💂🏻‍♂️💂🏻‍♂💂🏼‍♂️💂🏼‍♂💂🏽‍♂️💂🏽‍♂💂🏾‍♂️💂🏾‍♂💂🏿‍♂️💂🏿‍♂💂‍♀️💂‍♀💂🏻‍♀️💂🏻‍♀💂🏼‍♀️💂🏼‍♀💂🏽‍♀️💂🏽‍♀💂🏾‍♀️💂🏾‍♀💂🏿‍♀️💂🏿‍♀👷👷🏻👷🏼👷🏽👷🏾👷🏿👷‍♂️👷‍♂👷🏻‍♂️👷🏻‍♂👷🏼‍♂️👷🏼‍♂👷🏽‍♂️👷🏽‍♂👷🏾‍♂️👷🏾‍♂👷🏿‍♂️👷🏿‍♂👷‍♀️👷‍♀👷🏻‍♀️👷🏻‍♀👷🏼‍♀️👷🏼‍♀👷🏽‍♀️👷🏽‍♀👷🏾‍♀️👷🏾‍♀👷🏿‍♀️👷🏿‍♀🤴🤴🏻🤴🏼🤴🏽🤴🏾🤴🏿👸👸🏻👸🏼👸🏽👸🏾👸🏿👳👳🏻👳🏼👳🏽👳🏾👳🏿👳‍♂️👳‍♂👳🏻‍♂️👳🏻‍♂👳🏼‍♂️👳🏼‍♂👳🏽‍♂️👳🏽‍♂👳🏾‍♂️👳🏾‍♂👳🏿‍♂️👳🏿‍♂👳‍♀️👳‍♀👳🏻‍♀️👳🏻‍♀👳🏼‍♀️👳🏼‍♀👳🏽‍♀️👳🏽‍♀👳🏾‍♀️👳🏾‍♀👳🏿‍♀️👳🏿‍♀👲👲🏻👲🏼👲🏽👲🏾👲🏿🧕🧕🏻🧕🏼🧕🏽🧕🏾🧕🏿🧔🧔🏻🧔🏼🧔🏽🧔🏾🧔🏿👱👱🏻👱🏼👱🏽👱🏾👱🏿👱‍♂️👱‍♂👱🏻‍♂️👱🏻‍♂👱🏼‍♂️👱🏼‍♂👱🏽‍♂️👱🏽‍♂👱🏾‍♂️👱🏾‍♂👱🏿‍♂️👱🏿‍♂👱‍♀️👱‍♀👱🏻‍♀️👱🏻‍♀👱🏼‍♀️👱🏼‍♀👱🏽‍♀️👱🏽‍♀👱🏾‍♀️👱🏾‍♀👱🏿‍♀️👱🏿‍♀👨‍🦰👨🏻‍🦰👨🏼‍🦰👨🏽‍🦰👨🏾‍🦰👨🏿‍🦰👩‍🦰👩🏻‍🦰👩🏼‍🦰👩🏽‍🦰👩🏾‍🦰👩🏿‍🦰👨‍🦱👨🏻‍🦱👨🏼‍🦱👨🏽‍🦱👨🏾‍🦱👨🏿‍🦱👩‍🦱👩🏻‍🦱👩🏼‍🦱👩🏽‍🦱👩🏾‍🦱👩🏿‍🦱👨‍🦲👨🏻‍🦲👨🏼‍🦲👨🏽‍🦲👨🏾‍🦲👨🏿‍🦲👩‍🦲👩🏻‍🦲👩🏼‍🦲👩🏽‍🦲👩🏾‍🦲👩🏿‍🦲👨‍🦳👨🏻‍🦳👨🏼‍🦳👨🏽‍🦳👨🏾‍🦳👨🏿‍🦳👩‍🦳👩🏻‍🦳👩🏼‍🦳👩🏽‍🦳👩🏾‍🦳👩🏿‍🦳🤵🤵🏻🤵🏼🤵🏽🤵🏾🤵🏿👰👰🏻👰🏼👰🏽👰🏾👰🏿🤰🤰🏻🤰🏼🤰🏽🤰🏾🤰🏿🤱🤱🏻🤱🏼🤱🏽🤱🏾🤱🏿👼👼🏻👼🏼👼🏽👼🏾👼🏿🎅🎅🏻🎅🏼🎅🏽🎅🏾🎅🏿🤶🤶🏻🤶🏼🤶🏽🤶🏾🤶🏿🦸🦸🏻🦸🏼🦸🏽🦸🏾🦸🏿🦸‍♀️🦸‍♀🦸🏻‍♀️🦸🏻‍♀🦸🏼‍♀️🦸🏼‍♀🦸🏽‍♀️🦸🏽‍♀🦸🏾‍♀️🦸🏾‍♀🦸🏿‍♀️🦸🏿‍♀🦸‍♂️🦸‍♂🦸🏻‍♂️🦸🏻‍♂🦸🏼‍♂️🦸🏼‍♂🦸🏽‍♂️🦸🏽‍♂🦸🏾‍♂️🦸🏾‍♂🦸🏿‍♂️🦸🏿‍♂🦹🦹🏻🦹🏼🦹🏽🦹🏾🦹🏿🦹‍♀️🦹‍♀🦹🏻‍♀️🦹🏻‍♀🦹🏼‍♀️🦹🏼‍♀🦹🏽‍♀️🦹🏽‍♀🦹🏾‍♀️🦹🏾‍♀🦹🏿‍♀️🦹🏿‍♀🦹‍♂️🦹‍♂🦹🏻‍♂️🦹🏻‍♂🦹🏼‍♂️🦹🏼‍♂🦹🏽‍♂️🦹🏽‍♂🦹🏾‍♂️🦹🏾‍♂🦹🏿‍♂️🦹🏿‍♂🧙🧙🏻🧙🏼🧙🏽🧙🏾🧙🏿🧙‍♀️🧙‍♀🧙🏻‍♀️🧙🏻‍♀🧙🏼‍♀️🧙🏼‍♀🧙🏽‍♀️🧙🏽‍♀🧙🏾‍♀️🧙🏾‍♀🧙🏿‍♀️🧙🏿‍♀🧙‍♂️🧙‍♂🧙🏻‍♂️🧙🏻‍♂🧙🏼‍♂️🧙🏼‍♂🧙🏽‍♂️🧙🏽‍♂🧙🏾‍♂️🧙🏾‍♂🧙🏿‍♂️🧙🏿‍♂🧚🧚🏻🧚🏼🧚🏽🧚🏾🧚🏿🧚‍♀️🧚‍♀🧚🏻‍♀️🧚🏻‍♀🧚🏼‍♀️🧚🏼‍♀🧚🏽‍♀️🧚🏽‍♀🧚🏾‍♀️🧚🏾‍♀🧚🏿‍♀️🧚🏿‍♀🧚‍♂️🧚‍♂🧚🏻‍♂️🧚🏻‍♂🧚🏼‍♂️🧚🏼‍♂🧚🏽‍♂️🧚🏽‍♂🧚🏾‍♂️🧚🏾‍♂🧚🏿‍♂️🧚🏿‍♂🧛🧛🏻🧛🏼🧛🏽🧛🏾🧛🏿🧛‍♀️🧛‍♀🧛🏻‍♀️🧛🏻‍♀🧛🏼‍♀️🧛🏼‍♀🧛🏽‍♀️🧛🏽‍♀🧛🏾‍♀️🧛🏾‍♀🧛🏿‍♀️🧛🏿‍♀🧛‍♂️🧛‍♂🧛🏻‍♂️🧛🏻‍♂🧛🏼‍♂️🧛🏼‍♂🧛🏽‍♂️🧛🏽‍♂🧛🏾‍♂️🧛🏾‍♂🧛🏿‍♂️🧛🏿‍♂🧜🧜🏻🧜🏼🧜🏽🧜🏾🧜🏿🧜‍♀️🧜‍♀🧜🏻‍♀️🧜🏻‍♀🧜🏼‍♀️🧜🏼‍♀🧜🏽‍♀️🧜🏽‍♀🧜🏾‍♀️🧜🏾‍♀🧜🏿‍♀️🧜🏿‍♀🧜‍♂️🧜‍♂🧜🏻‍♂️🧜🏻‍♂🧜🏼‍♂️🧜🏼‍♂🧜🏽‍♂️🧜🏽‍♂🧜🏾‍♂️🧜🏾‍♂🧜🏿‍♂️🧜🏿‍♂🧝🧝🏻🧝🏼🧝🏽🧝🏾🧝🏿🧝‍♀️🧝‍♀🧝🏻‍♀️🧝🏻‍♀🧝🏼‍♀️🧝🏼‍♀🧝🏽‍♀️🧝🏽‍♀🧝🏾‍♀️🧝🏾‍♀🧝🏿‍♀️🧝🏿‍♀🧝‍♂️🧝‍♂🧝🏻‍♂️🧝🏻‍♂🧝🏼‍♂️🧝🏼‍♂🧝🏽‍♂️🧝🏽‍♂🧝🏾‍♂️🧝🏾‍♂🧝🏿‍♂️🧝🏿‍♂🧞🧞‍♀️🧞‍♀🧞‍♂️🧞‍♂🧟🧟‍♀️🧟‍♀🧟‍♂️🧟‍♂🙍🙍🏻🙍🏼🙍🏽🙍🏾🙍🏿🙍‍♂️🙍‍♂🙍🏻‍♂️🙍🏻‍♂🙍🏼‍♂️🙍🏼‍♂🙍🏽‍♂️🙍🏽‍♂🙍🏾‍♂️🙍🏾‍♂🙍🏿‍♂️🙍🏿‍♂🙍‍♀️🙍‍♀🙍🏻‍♀️🙍🏻‍♀🙍🏼‍♀️🙍🏼‍♀🙍🏽‍♀️🙍🏽‍♀🙍🏾‍♀️🙍🏾‍♀🙍🏿‍♀️🙍🏿‍♀🙎🙎🏻🙎🏼🙎🏽🙎🏾🙎🏿🙎‍♂️🙎‍♂🙎🏻‍♂️🙎🏻‍♂🙎🏼‍♂️🙎🏼‍♂🙎🏽‍♂️🙎🏽‍♂🙎🏾‍♂️🙎🏾‍♂🙎🏿‍♂️🙎🏿‍♂🙎‍♀️🙎‍♀🙎🏻‍♀️🙎🏻‍♀🙎🏼‍♀️🙎🏼‍♀🙎🏽‍♀️🙎🏽‍♀🙎🏾‍♀️🙎🏾‍♀🙎🏿‍♀️🙎🏿‍♀🙅🙅🏻🙅🏼🙅🏽🙅🏾🙅🏿🙅‍♂️🙅‍♂🙅🏻‍♂️🙅🏻‍♂🙅🏼‍♂️🙅🏼‍♂🙅🏽‍♂️🙅🏽‍♂🙅🏾‍♂️🙅🏾‍♂🙅🏿‍♂️🙅🏿‍♂🙅‍♀️🙅‍♀🙅🏻‍♀️🙅🏻‍♀🙅🏼‍♀️🙅🏼‍♀🙅🏽‍♀️🙅🏽‍♀🙅🏾‍♀️🙅🏾‍♀🙅🏿‍♀️🙅🏿‍♀🙆🙆🏻🙆🏼🙆🏽🙆🏾🙆🏿🙆‍♂️🙆‍♂🙆🏻‍♂️🙆🏻‍♂🙆🏼‍♂️🙆🏼‍♂🙆🏽‍♂️🙆🏽‍♂🙆🏾‍♂️🙆🏾‍♂🙆🏿‍♂️🙆🏿‍♂🙆‍♀️🙆‍♀🙆🏻‍♀️🙆🏻‍♀🙆🏼‍♀️🙆🏼‍♀🙆🏽‍♀️🙆🏽‍♀🙆🏾‍♀️🙆🏾‍♀🙆🏿‍♀️🙆🏿‍♀💁💁🏻💁🏼💁🏽💁🏾💁🏿💁‍♂️💁‍♂💁🏻‍♂️💁🏻‍♂💁🏼‍♂️💁🏼‍♂💁🏽‍♂️💁🏽‍♂💁🏾‍♂️💁🏾‍♂💁🏿‍♂️💁🏿‍♂💁‍♀️💁‍♀💁🏻‍♀️💁🏻‍♀💁🏼‍♀️💁🏼‍♀💁🏽‍♀️💁🏽‍♀💁🏾‍♀️💁🏾‍♀💁🏿‍♀️💁🏿‍♀🙋🙋🏻🙋🏼🙋🏽🙋🏾🙋🏿🙋‍♂️🙋‍♂🙋🏻‍♂️🙋🏻‍♂🙋🏼‍♂️🙋🏼‍♂🙋🏽‍♂️🙋🏽‍♂🙋🏾‍♂️🙋🏾‍♂🙋🏿‍♂️🙋🏿‍♂🙋‍♀️🙋‍♀🙋🏻‍♀️🙋🏻‍♀🙋🏼‍♀️🙋🏼‍♀🙋🏽‍♀️🙋🏽‍♀🙋🏾‍♀️🙋🏾‍♀🙋🏿‍♀️🙋🏿‍♀🙇🙇🏻🙇🏼🙇🏽🙇🏾🙇🏿🙇‍♂️🙇‍♂🙇🏻‍♂️🙇🏻‍♂🙇🏼‍♂️🙇🏼‍♂🙇🏽‍♂️🙇🏽‍♂🙇🏾‍♂️🙇🏾‍♂🙇🏿‍♂️🙇🏿‍♂🙇‍♀️🙇‍♀🙇🏻‍♀️🙇🏻‍♀🙇🏼‍♀️🙇🏼‍♀🙇🏽‍♀️🙇🏽‍♀🙇🏾‍♀️🙇🏾‍♀🙇🏿‍♀️🙇🏿‍♀🤦🤦🏻🤦🏼🤦🏽🤦🏾🤦🏿🤦‍♂️🤦‍♂🤦🏻‍♂️🤦🏻‍♂🤦🏼‍♂️🤦🏼‍♂🤦🏽‍♂️🤦🏽‍♂🤦🏾‍♂️🤦🏾‍♂🤦🏿‍♂️🤦🏿‍♂🤦‍♀️🤦‍♀🤦🏻‍♀️🤦🏻‍♀🤦🏼‍♀️🤦🏼‍♀🤦🏽‍♀️🤦🏽‍♀🤦🏾‍♀️🤦🏾‍♀🤦🏿‍♀️🤦🏿‍♀🤷🤷🏻🤷🏼🤷🏽🤷🏾🤷🏿🤷‍♂️🤷‍♂🤷🏻‍♂️🤷🏻‍♂🤷🏼‍♂️🤷🏼‍♂🤷🏽‍♂️🤷🏽‍♂🤷🏾‍♂️🤷🏾‍♂🤷🏿‍♂️🤷🏿‍♂🤷‍♀️🤷‍♀🤷🏻‍♀️🤷🏻‍♀🤷🏼‍♀️🤷🏼‍♀🤷🏽‍♀️🤷🏽‍♀🤷🏾‍♀️🤷🏾‍♀🤷🏿‍♀️🤷🏿‍♀💆💆🏻💆🏼💆🏽💆🏾💆🏿💆‍♂️💆‍♂💆🏻‍♂️💆🏻‍♂💆🏼‍♂️💆🏼‍♂💆🏽‍♂️💆🏽‍♂💆🏾‍♂️💆🏾‍♂💆🏿‍♂️💆🏿‍♂💆‍♀️💆‍♀💆🏻‍♀️💆🏻‍♀💆🏼‍♀️💆🏼‍♀💆🏽‍♀️💆🏽‍♀💆🏾‍♀️💆🏾‍♀💆🏿‍♀️💆🏿‍♀💇💇🏻💇🏼💇🏽💇🏾💇🏿💇‍♂️💇‍♂💇🏻‍♂️💇🏻‍♂💇🏼‍♂️💇🏼‍♂💇🏽‍♂️💇🏽‍♂💇🏾‍♂️💇🏾‍♂💇🏿‍♂️💇🏿‍♂💇‍♀️💇‍♀💇🏻‍♀️💇🏻‍♀💇🏼‍♀️💇🏼‍♀💇🏽‍♀️💇🏽‍♀💇🏾‍♀️💇🏾‍♀💇🏿‍♀️💇🏿‍♀🚶🚶🏻🚶🏼🚶🏽🚶🏾🚶🏿🚶‍♂️🚶‍♂🚶🏻‍♂️🚶🏻‍♂🚶🏼‍♂️🚶🏼‍♂🚶🏽‍♂️🚶🏽‍♂🚶🏾‍♂️🚶🏾‍♂🚶🏿‍♂️🚶🏿‍♂🚶‍♀️🚶‍♀🚶🏻‍♀️🚶🏻‍♀🚶🏼‍♀️🚶🏼‍♀🚶🏽‍♀️🚶🏽‍♀🚶🏾‍♀️🚶🏾‍♀🚶🏿‍♀️🚶🏿‍♀🏃🏃🏻🏃🏼🏃🏽🏃🏾🏃🏿🏃‍♂️🏃‍♂🏃🏻‍♂️🏃🏻‍♂🏃🏼‍♂️🏃🏼‍♂🏃🏽‍♂️🏃🏽‍♂🏃🏾‍♂️🏃🏾‍♂🏃🏿‍♂️🏃🏿‍♂🏃‍♀️🏃‍♀🏃🏻‍♀️🏃🏻‍♀🏃🏼‍♀️🏃🏼‍♀🏃🏽‍♀️🏃🏽‍♀🏃🏾‍♀️🏃🏾‍♀🏃🏿‍♀️🏃🏿‍♀💃💃🏻💃🏼💃🏽💃🏾💃🏿🕺🕺🏻🕺🏼🕺🏽🕺🏾🕺🏿👯👯‍♂️👯‍♂👯‍♀️👯‍♀🧖🧖🏻🧖🏼🧖🏽🧖🏾🧖🏿🧖‍♀️🧖‍♀🧖🏻‍♀️🧖🏻‍♀🧖🏼‍♀️🧖🏼‍♀🧖🏽‍♀️🧖🏽‍♀🧖🏾‍♀️🧖🏾‍♀🧖🏿‍♀️🧖🏿‍♀🧖‍♂️🧖‍♂🧖🏻‍♂️🧖🏻‍♂🧖🏼‍♂️🧖🏼‍♂🧖🏽‍♂️🧖🏽‍♂🧖🏾‍♂️🧖🏾‍♂🧖🏿‍♂️🧖🏿‍♂🧗🧗🏻🧗🏼🧗🏽🧗🏾🧗🏿🧗‍♀️🧗‍♀🧗🏻‍♀️🧗🏻‍♀🧗🏼‍♀️🧗🏼‍♀🧗🏽‍♀️🧗🏽‍♀🧗🏾‍♀️🧗🏾‍♀🧗🏿‍♀️🧗🏿‍♀🧗‍♂️🧗‍♂🧗🏻‍♂️🧗🏻‍♂🧗🏼‍♂️🧗🏼‍♂🧗🏽‍♂️🧗🏽‍♂🧗🏾‍♂️🧗🏾‍♂🧗🏿‍♂️🧗🏿‍♂🧘🧘🏻🧘🏼🧘🏽🧘🏾🧘🏿🧘‍♀️🧘‍♀🧘🏻‍♀️🧘🏻‍♀🧘🏼‍♀️🧘🏼‍♀🧘🏽‍♀️🧘🏽‍♀🧘🏾‍♀️🧘🏾‍♀🧘🏿‍♀️🧘🏿‍♀🧘‍♂️🧘‍♂🧘🏻‍♂️🧘🏻‍♂🧘🏼‍♂️🧘🏼‍♂🧘🏽‍♂️🧘🏽‍♂🧘🏾‍♂️🧘🏾‍♂🧘🏿‍♂️🧘🏿‍♂🛀🛀🏻🛀🏼🛀🏽🛀🏾🛀🏿🛌🛌🏻🛌🏼🛌🏽🛌🏾🛌🏿🕴️🕴🕴🏻🕴🏼🕴🏽🕴🏾🕴🏿🗣️🗣👤👥🤺🏇🏇🏻🏇🏼🏇🏽🏇🏾🏇🏿⛷️⛷🏂🏂🏻🏂🏼🏂🏽🏂🏾🏂🏿🏌️🏌🏌🏻🏌🏼🏌🏽🏌🏾🏌🏿🏌️‍♂️🏌‍♂️🏌️‍♂🏌‍♂🏌🏻‍♂️🏌🏻‍♂🏌🏼‍♂️🏌🏼‍♂🏌🏽‍♂️🏌🏽‍♂🏌🏾‍♂️🏌🏾‍♂🏌🏿‍♂️🏌🏿‍♂🏌️‍♀️🏌‍♀️🏌️‍♀🏌‍♀🏌🏻‍♀️🏌🏻‍♀🏌🏼‍♀️🏌🏼‍♀🏌🏽‍♀️🏌🏽‍♀🏌🏾‍♀️🏌🏾‍♀🏌🏿‍♀️🏌🏿‍♀🏄🏄🏻🏄🏼🏄🏽🏄🏾🏄🏿🏄‍♂️🏄‍♂🏄🏻‍♂️🏄🏻‍♂🏄🏼‍♂️🏄🏼‍♂🏄🏽‍♂️🏄🏽‍♂🏄🏾‍♂️🏄🏾‍♂🏄🏿‍♂️🏄🏿‍♂🏄‍♀️🏄‍♀🏄🏻‍♀️🏄🏻‍♀🏄🏼‍♀️🏄🏼‍♀🏄🏽‍♀️🏄🏽‍♀🏄🏾‍♀️🏄🏾‍♀🏄🏿‍♀️🏄🏿‍♀🚣🚣🏻🚣🏼🚣🏽🚣🏾🚣🏿🚣‍♂️🚣‍♂🚣🏻‍♂️🚣🏻‍♂🚣🏼‍♂️🚣🏼‍♂🚣🏽‍♂️🚣🏽‍♂🚣🏾‍♂️🚣🏾‍♂🚣🏿‍♂️🚣🏿‍♂🚣‍♀️🚣‍♀🚣🏻‍♀️🚣🏻‍♀🚣🏼‍♀️🚣🏼‍♀🚣🏽‍♀️🚣🏽‍♀🚣🏾‍♀️🚣🏾‍♀🚣🏿‍♀️🚣🏿‍♀🏊🏊🏻🏊🏼🏊🏽🏊🏾🏊🏿🏊‍♂️🏊‍♂🏊🏻‍♂️🏊🏻‍♂🏊🏼‍♂️🏊🏼‍♂🏊🏽‍♂️🏊🏽‍♂🏊🏾‍♂️🏊🏾‍♂🏊🏿‍♂️🏊🏿‍♂🏊‍♀️🏊‍♀🏊🏻‍♀️🏊🏻‍♀🏊🏼‍♀️🏊🏼‍♀🏊🏽‍♀️🏊🏽‍♀🏊🏾‍♀️🏊🏾‍♀🏊🏿‍♀️🏊🏿‍♀⛹️⛹⛹🏻⛹🏼⛹🏽⛹🏾⛹🏿⛹️‍♂️⛹‍♂️⛹️‍♂⛹‍♂⛹🏻‍♂️⛹🏻‍♂⛹🏼‍♂️⛹🏼‍♂⛹🏽‍♂️⛹🏽‍♂⛹🏾‍♂️⛹🏾‍♂⛹🏿‍♂️⛹🏿‍♂⛹️‍♀️⛹‍♀️⛹️‍♀⛹‍♀⛹🏻‍♀️⛹🏻‍♀⛹🏼‍♀️⛹🏼‍♀⛹🏽‍♀️⛹🏽‍♀⛹🏾‍♀️⛹🏾‍♀⛹🏿‍♀️⛹🏿‍♀🏋️🏋🏋🏻🏋🏼🏋🏽🏋🏾🏋🏿🏋️‍♂️🏋‍♂️🏋️‍♂🏋‍♂🏋🏻‍♂️🏋🏻‍♂🏋🏼‍♂️🏋🏼‍♂🏋🏽‍♂️🏋🏽‍♂🏋🏾‍♂️🏋🏾‍♂🏋🏿‍♂️🏋🏿‍♂🏋️‍♀️🏋‍♀️🏋️‍♀🏋‍♀🏋🏻‍♀️🏋🏻‍♀🏋🏼‍♀️🏋🏼‍♀🏋🏽‍♀️🏋🏽‍♀🏋🏾‍♀️🏋🏾‍♀🏋🏿‍♀️🏋🏿‍♀🚴🚴🏻🚴🏼🚴🏽🚴🏾🚴🏿🚴‍♂️🚴‍♂🚴🏻‍♂️🚴🏻‍♂🚴🏼‍♂️🚴🏼‍♂🚴🏽‍♂️🚴🏽‍♂🚴🏾‍♂️🚴🏾‍♂🚴🏿‍♂️🚴🏿‍♂🚴‍♀️🚴‍♀🚴🏻‍♀️🚴🏻‍♀🚴🏼‍♀️🚴🏼‍♀🚴🏽‍♀️🚴🏽‍♀🚴🏾‍♀️🚴🏾‍♀🚴🏿‍♀️🚴🏿‍♀🚵🚵🏻🚵🏼🚵🏽🚵🏾🚵🏿🚵‍♂️🚵‍♂🚵🏻‍♂️🚵🏻‍♂🚵🏼‍♂️🚵🏼‍♂🚵🏽‍♂️🚵🏽‍♂🚵🏾‍♂️🚵🏾‍♂🚵🏿‍♂️🚵🏿‍♂🚵‍♀️🚵‍♀🚵🏻‍♀️🚵🏻‍♀🚵🏼‍♀️🚵🏼‍♀🚵🏽‍♀️🚵🏽‍♀🚵🏾‍♀️🚵🏾‍♀🚵🏿‍♀️🚵🏿‍♀🏎️🏎🏍️🏍🤸🤸🏻🤸🏼🤸🏽🤸🏾🤸🏿🤸‍♂️🤸‍♂🤸🏻‍♂️🤸🏻‍♂🤸🏼‍♂️🤸🏼‍♂🤸🏽‍♂️🤸🏽‍♂🤸🏾‍♂️🤸🏾‍♂🤸🏿‍♂️🤸🏿‍♂🤸‍♀️🤸‍♀🤸🏻‍♀️🤸🏻‍♀🤸🏼‍♀️🤸🏼‍♀🤸🏽‍♀️🤸🏽‍♀🤸🏾‍♀️🤸🏾‍♀🤸🏿‍♀️🤸🏿‍♀🤼🤼‍♂️🤼‍♂🤼‍♀️🤼‍♀🤽🤽🏻🤽🏼🤽🏽🤽🏾🤽🏿🤽‍♂️🤽‍♂🤽🏻‍♂️🤽🏻‍♂🤽🏼‍♂️🤽🏼‍♂🤽🏽‍♂️🤽🏽‍♂🤽🏾‍♂️🤽🏾‍♂🤽🏿‍♂️🤽🏿‍♂🤽‍♀️🤽‍♀🤽🏻‍♀️🤽🏻‍♀🤽🏼‍♀️🤽🏼‍♀🤽🏽‍♀️🤽🏽‍♀🤽🏾‍♀️🤽🏾‍♀🤽🏿‍♀️🤽🏿‍♀🤾🤾🏻🤾🏼🤾🏽🤾🏾🤾🏿🤾‍♂️🤾‍♂🤾🏻‍♂️🤾🏻‍♂🤾🏼‍♂️🤾🏼‍♂🤾🏽‍♂️🤾🏽‍♂🤾🏾‍♂️🤾🏾‍♂🤾🏿‍♂️🤾🏿‍♂🤾‍♀️🤾‍♀🤾🏻‍♀️🤾🏻‍♀🤾🏼‍♀️🤾🏼‍♀🤾🏽‍♀️🤾🏽‍♀🤾🏾‍♀️🤾🏾‍♀🤾🏿‍♀️🤾🏿‍♀🤹🤹🏻🤹🏼🤹🏽🤹🏾🤹🏿🤹‍♂️🤹‍♂🤹🏻‍♂️🤹🏻‍♂🤹🏼‍♂️🤹🏼‍♂🤹🏽‍♂️🤹🏽‍♂🤹🏾‍♂️🤹🏾‍♂🤹🏿‍♂️🤹🏿‍♂🤹‍♀️🤹‍♀🤹🏻‍♀️🤹🏻‍♀🤹🏼‍♀️🤹🏼‍♀🤹🏽‍♀️🤹🏽‍♀🤹🏾‍♀️🤹🏾‍♀🤹🏿‍♀️🤹🏿‍♀👫👬👭💏👩‍❤️‍💋‍👨👩‍❤‍💋‍👨👨‍❤️‍💋‍👨👨‍❤‍💋‍👨👩‍❤️‍💋‍👩👩‍❤‍💋‍👩💑👩‍❤️‍👨👩‍❤‍👨👨‍❤️‍👨👨‍❤‍👨👩‍❤️‍👩👩‍❤‍👩👪👨‍👩‍👦👨‍👩‍👧👨‍👩‍👧‍👦👨‍👩‍👦‍👦👨‍👩‍👧‍👧👨‍👨‍👦👨‍👨‍👧👨‍👨‍👧‍👦👨‍👨‍👦‍👦👨‍👨‍👧‍👧👩‍👩‍👦👩‍👩‍👧👩‍👩‍👧‍👦👩‍👩‍👦‍👦👩‍👩‍👧‍👧👨‍👦👨‍👦‍👦👨‍👧👨‍👧‍👦👨‍👧‍👧👩‍👦👩‍👦‍👦👩‍👧👩‍👧‍👦👩‍👧‍👧🤳🤳🏻🤳🏼🤳🏽🤳🏾🤳🏿💪💪🏻💪🏼💪🏽💪🏾💪🏿🦵🦵🏻🦵🏼🦵🏽🦵🏾🦵🏿🦶🦶🏻🦶🏼🦶🏽🦶🏾🦶🏿👈👈🏻👈🏼👈🏽👈🏾👈🏿👉👉🏻👉🏼👉🏽👉🏾👉🏿☝️☝☝🏻☝🏼☝🏽☝🏾☝🏿👆👆🏻👆🏼👆🏽👆🏾👆🏿🖕🖕🏻🖕🏼🖕🏽🖕🏾🖕🏿👇👇🏻👇🏼👇🏽👇🏾👇🏿✌️✌✌🏻✌🏼✌🏽✌🏾✌🏿🤞🤞🏻🤞🏼🤞🏽🤞🏾🤞🏿🖖🖖🏻🖖🏼🖖🏽🖖🏾🖖🏿🤘🤘🏻🤘🏼🤘🏽🤘🏾🤘🏿🤙🤙🏻🤙🏼🤙🏽🤙🏾🤙🏿🖐️🖐🖐🏻🖐🏼🖐🏽🖐🏾🖐🏿✋✋🏻✋🏼✋🏽✋🏾✋🏿👌👌🏻👌🏼👌🏽👌🏾👌🏿👍👍🏻👍🏼👍🏽👍🏾👍🏿👎👎🏻👎🏼👎🏽👎🏾👎🏿✊✊🏻✊🏼✊🏽✊🏾✊🏿👊👊🏻👊🏼👊🏽👊🏾👊🏿🤛🤛🏻🤛🏼🤛🏽🤛🏾🤛🏿🤜🤜🏻🤜🏼🤜🏽🤜🏾🤜🏿🤚🤚🏻🤚🏼🤚🏽🤚🏾🤚🏿👋👋🏻👋🏼👋🏽👋🏾👋🏿🤟🤟🏻🤟🏼🤟🏽🤟🏾🤟🏿✍️✍✍🏻✍🏼✍🏽✍🏾✍🏿👏👏🏻👏🏼👏🏽👏🏾👏🏿👐👐🏻👐🏼👐🏽👐🏾👐🏿🙌🙌🏻🙌🏼🙌🏽🙌🏾🙌🏿🤲🤲🏻🤲🏼🤲🏽🤲🏾🤲🏿🙏🙏🏻🙏🏼🙏🏽🙏🏾🙏🏿🤝💅💅🏻💅🏼💅🏽💅🏾💅🏿👂👂🏻👂🏼👂🏽👂🏾👂🏿👃👃🏻👃🏼👃🏽👃🏾👃🏿🦰🦱🦲🦳👣👀👁️👁👁️‍🗨️👁‍🗨️👁️‍🗨👁‍🗨🧠🦴🦷👅👄💋💘❤️❤💓💔💕💖💗💙💚💛🧡💜🖤💝💞💟❣️❣💌💤💢💣💥💦💨💫💬🗨️🗨🗯️🗯💭🕳️🕳👓🕶️🕶🥽🥼👔👕👖🧣🧤🧥🧦👗👘👙👚👛👜👝🛍️🛍🎒👞👟🥾🥿👠👡👢👑👒🎩🎓🧢⛑️⛑📿💄💍💎🐵🐒🦍🐶🐕🐩🐺🦊🦝🐱🐈🦁🐯🐅🐆🐴🐎🦄🦓🦌🐮🐂🐃🐄🐷🐖🐗🐽🐏🐑🐐🐪🐫🦙🦒🐘🦏🦛🐭🐁🐀🐹🐰🐇🐿️🐿🦔🦇🐻🐨🐼🦘🦡🐾🦃🐔🐓🐣🐤🐥🐦🐧🕊️🕊🦅🦆🦢🦉🦚🦜🐸🐊🐢🦎🐍🐲🐉🦕🦖🐳🐋🐬🐟🐠🐡🦈🐙🐚🦀🦞🦐🦑🐌🦋🐛🐜🐝🐞🦗🕷️🕷🕸️🕸🦂🦟🦠💐🌸💮🏵️🏵🌹🥀🌺🌻🌼🌷🌱🌲🌳🌴🌵🌾🌿☘️☘🍀🍁🍂🍃🍇🍈🍉🍊🍋🍌🍍🥭🍎🍏🍐🍑🍒🍓🥝🍅🥥🥑🍆🥔🥕🌽🌶️🌶🥒🥬🥦🍄🥜🌰🍞🥐🥖🥨🥯🥞🧀🍖🍗🥩🥓🍔🍟🍕🌭🥪🌮🌯🥙🥚🍳🥘🍲🥣🥗🍿🧂🥫🍱🍘🍙🍚🍛🍜🍝🍠🍢🍣🍤🍥🥮🍡🥟🥠🥡🍦🍧🍨🍩🍪🎂🍰🧁🥧🍫🍬🍭🍮🍯🍼🥛☕🍵🍶🍾🍷🍸🍹🍺🍻🥂🥃🥤🥢🍽️🍽🍴🥄🔪🏺🌍🌎🌏🌐🗺️🗺🗾🧭🏔️🏔⛰️⛰🌋🗻🏕️🏕🏖️🏖🏜️🏜🏝️🏝🏞️🏞🏟️🏟🏛️🏛🏗️🏗🧱🏘️🏘🏚️🏚🏠🏡🏢🏣🏤🏥🏦🏨🏩🏪🏫🏬🏭🏯🏰💒🗼🗽⛪🕌🕍⛩️⛩🕋⛲⛺🌁🌃🏙️🏙🌄🌅🌆🌇🌉♨️♨🌌🎠🎡🎢💈🎪🚂🚃🚄🚅🚆🚇🚈🚉🚊🚝🚞🚋🚌🚍🚎🚐🚑🚒🚓🚔🚕🚖🚗🚘🚙🚚🚛🚜🚲🛴🛹🛵🚏🛣️🛣🛤️🛤🛢️🛢⛽🚨🚥🚦🛑🚧⚓⛵🛶🚤🛳️🛳⛴️⛴🛥️🛥🚢✈️✈🛩️🛩🛫🛬💺🚁🚟🚠🚡🛰️🛰🚀🛸🛎️🛎🧳⌛⏳⌚⏰⏱️⏱⏲️⏲🕰️🕰🕛🕧🕐🕜🕑🕝🕒🕞🕓🕟🕔🕠🕕🕡🕖🕢🕗🕣🕘🕤🕙🕥🕚🕦🌑🌒🌓🌔🌕🌖🌗🌘🌙🌚🌛🌜🌡️🌡☀️☀🌝🌞⭐🌟🌠☁️☁⛅⛈️⛈🌤️🌤🌥️🌥🌦️🌦🌧️🌧🌨️🌨🌩️🌩🌪️🌪🌫️🌫🌬️🌬🌀🌈🌂☂️☂☔⛱️⛱⚡❄️❄☃️☃⛄☄️☄🔥💧🌊🎃🎄🎆🎇🧨✨🎈🎉🎊🎋🎍🎎🎏🎐🎑🧧🎀🎁🎗️🎗🎟️🎟🎫🎖️🎖🏆🏅🥇🥈🥉⚽⚾🥎🏀🏐🏈🏉🎾🥏🎳🏏🏑🏒🥍🏓🏸🥊🥋🥅⛳⛸️⛸🎣🎽🎿🛷🥌🎯🎱🔮🧿🎮🕹️🕹🎰🎲🧩🧸♠️♠♥️♥♦️♦♣️♣♟️♟🃏🀄🎴🎭🖼️🖼🎨🧵🧶🔇🔈🔉🔊📢📣📯🔔🔕🎼🎵🎶🎙️🎙🎚️🎚🎛️🎛🎤🎧📻🎷🎸🎹🎺🎻🥁📱📲☎️☎📞📟📠🔋🔌💻🖥️🖥🖨️🖨⌨️⌨🖱️🖱🖲️🖲💽💾💿📀🧮🎥🎞️🎞📽️📽🎬📺📷📸📹📼🔍🔎🕯️🕯💡🔦🏮📔📕📖📗📘📙📚📓📒📃📜📄📰🗞️🗞📑🔖🏷️🏷💰💴💵💶💷💸💳🧾💹💱💲✉️✉📧📨📩📤📥📦📫📪📬📭📮🗳️🗳✏️✏✒️✒🖋️🖋🖊️🖊🖌️🖌🖍️🖍📝💼📁📂🗂️🗂📅📆🗒️🗒🗓️🗓📇📈📉📊📋📌📍📎🖇️🖇📏📐✂️✂🗃️🗃🗄️🗄🗑️🗑🔒🔓🔏🔐🔑🗝️🗝🔨⛏️⛏⚒️⚒🛠️🛠🗡️🗡⚔️⚔🔫🏹🛡️🛡🔧🔩⚙️⚙🗜️🗜⚖️⚖🔗⛓️⛓🧰🧲⚗️⚗🧪🧫🧬🔬🔭📡💉💊🚪🛏️🛏🛋️🛋🚽🚿🛁🧴🧷🧹🧺🧻🧼🧽🧯🛒🚬⚰️⚰⚱️⚱🗿🏧🚮🚰♿🚹🚺🚻🚼🚾🛂🛃🛄🛅⚠️⚠🚸⛔🚫🚳🚭🚯🚱🚷📵🔞☢️☢☣️☣⬆️⬆↗️↗➡️➡↘️↘⬇️⬇↙️↙⬅️⬅↖️↖↕️↕↔️↔↩️↩↪️↪⤴️⤴⤵️⤵🔃🔄🔙🔚🔛🔜🔝🛐⚛️⚛🕉️🕉✡️✡☸️☸☯️☯✝️✝☦️☦☪️☪☮️☮🕎🔯♈♉♊♋♌♍♎♏♐♑♒♓⛎🔀🔁🔂▶️▶⏩⏭️⏭⏯️⏯◀️◀⏪⏮️⏮🔼⏫🔽⏬⏸️⏸⏹️⏹⏺️⏺⏏️⏏🎦🔅🔆📶📳📴♀️♀♂️♂⚕️⚕♾️♾♻️♻⚜️⚜🔱📛🔰⭕✅☑️☑✔️✔✖️✖❌❎➕➖➗➰➿〽️〽✳️✳✴️✴❇️❇‼️‼⁉️⁉❓❔❕❗〰️〰©️©®️®™️™#️⃣#⃣*️⃣*⃣0️⃣0⃣1️⃣1⃣2️⃣2⃣3️⃣3⃣4️⃣4⃣5️⃣5⃣6️⃣6⃣7️⃣7⃣8️⃣8⃣9️⃣9⃣🔟💯🔠🔡🔢🔣🔤🅰️🅰🆎🅱️🅱🆑🆒🆓ℹ️ℹ🆔Ⓜ️Ⓜ🆕🆖🅾️🅾🆗🅿️🅿🆘🆙🆚🈁🈂️🈂🈷️🈷🈶🈯🉐🈹🈚🈲🉑🈸🈴🈳㊗️㊗㊙️㊙🈺🈵▪️▪▫️▫◻️◻◼️◼◽◾⬛⬜🔶🔷🔸🔹🔺🔻💠🔘🔲🔳⚪⚫🔴🔵🏁🚩🎌🏴🏳️🏳🏳️‍🌈🏳‍🌈🏴‍☠️🏴‍☠🇦🇨🇦🇩🇦🇪🇦🇫🇦🇬🇦🇮🇦🇱🇦🇲🇦🇴🇦🇶🇦🇷🇦🇸🇦🇹🇦🇺🇦🇼🇦🇽🇦🇿🇧🇦🇧🇧🇧🇩🇧🇪🇧🇫🇧🇬🇧🇭🇧🇮🇧🇯🇧🇱🇧🇲🇧🇳🇧🇴🇧🇶🇧🇷🇧🇸🇧🇹🇧🇻🇧🇼🇧🇾🇧🇿🇨🇦🇨🇨🇨🇩🇨🇫🇨🇬🇨🇭🇨🇮🇨🇰🇨🇱🇨🇲🇨🇳🇨🇴🇨🇵🇨🇷🇨🇺🇨🇻🇨🇼🇨🇽🇨🇾🇨🇿🇩🇪🇩🇬🇩🇯🇩🇰🇩🇲🇩🇴🇩🇿🇪🇦🇪🇨🇪🇪🇪🇬🇪🇭🇪🇷🇪🇸🇪🇹🇪🇺🇫🇮🇫🇯🇫🇰🇫🇲🇫🇴🇫🇷🇬🇦🇬🇧🇬🇩🇬🇪🇬🇫🇬🇬🇬🇭🇬🇮🇬🇱🇬🇲🇬🇳🇬🇵🇬🇶🇬🇷🇬🇸🇬🇹🇬🇺🇬🇼🇬🇾🇭🇰🇭🇲🇭🇳🇭🇷🇭🇹🇭🇺🇮🇨🇮🇩🇮🇪🇮🇱🇮🇲🇮🇳🇮🇴🇮🇶🇮🇷🇮🇸🇮🇹🇯🇪🇯🇲🇯🇴🇯🇵🇰🇪🇰🇬🇰🇭🇰🇮🇰🇲🇰🇳🇰🇵🇰🇷🇰🇼🇰🇾🇰🇿🇱🇦🇱🇧🇱🇨🇱🇮🇱🇰🇱🇷🇱🇸🇱🇹🇱🇺🇱🇻🇱🇾🇲🇦🇲🇨🇲🇩🇲🇪🇲🇫🇲🇬🇲🇭🇲🇰🇲🇱🇲🇲🇲🇳🇲🇴🇲🇵🇲🇶🇲🇷🇲🇸🇲🇹🇲🇺🇲🇻🇲🇼🇲🇽🇲🇾🇲🇿🇳🇦🇳🇨🇳🇪🇳🇫🇳🇬🇳🇮🇳🇱🇳🇴🇳🇵🇳🇷🇳🇺🇳🇿🇴🇲🇵🇦🇵🇪🇵🇫🇵🇬🇵🇭🇵🇰🇵🇱🇵🇲🇵🇳🇵🇷🇵🇸🇵🇹🇵🇼🇵🇾🇶🇦🇷🇪🇷🇴🇷🇸🇷🇺🇷🇼🇸🇦🇸🇧🇸🇨🇸🇩🇸🇪🇸🇬🇸🇭🇸🇮🇸🇯🇸🇰🇸🇱🇸🇲🇸🇳🇸🇴🇸🇷🇸🇸🇸🇹🇸🇻🇸🇽🇸🇾🇸🇿🇹🇦🇹🇨🇹🇩🇹🇫🇹🇬🇹🇭🇹🇯🇹🇰🇹🇱🇹🇲🇹🇳🇹🇴🇹🇷🇹🇹🇹🇻🇹🇼🇹🇿🇺🇦🇺🇬🇺🇲🇺🇳🇺🇸🇺🇾🇺🇿🇻🇦🇻🇨🇻🇪🇻🇬🇻🇮🇻🇳🇻🇺🇼🇫🇼🇸🇽🇰🇾🇪🇾🇹🇿🇦🇿🇲🇿🇼🏴󠁧󠁢󠁥󠁮󠁧󠁿🏴󠁧󠁢󠁳󠁣󠁴󠁿🏴󠁧󠁢󠁷󠁬󠁳󠁿");
    (0, vitest_1.expect)(() => emoji.parse(":-)")).toThrow();
    (0, vitest_1.expect)(() => emoji.parse("😀 is an emoji")).toThrow();
    (0, vitest_1.expect)(() => emoji.parse("😀stuff")).toThrow();
    (0, vitest_1.expect)(() => emoji.parse("stuff😀")).toThrow();
});
(0, vitest_1.test)("nanoid", () => {
    const nanoid = z.string().nanoid("custom error");
    nanoid.parse("lfNZluvAxMkf7Q8C5H-QS");
    nanoid.parse("mIU_4PJWikaU8fMbmkouz");
    nanoid.parse("Hb9ZUtUa2JDm_dD-47EGv");
    nanoid.parse("5Noocgv_8vQ9oPijj4ioQ");
    const result = nanoid.safeParse("Xq90uDyhddC53KsoASYJGX");
    (0, vitest_1.expect)(result).toMatchObject({ success: false });
    (0, vitest_1.expect)(result.error.issues[0].message).toEqual("custom error");
    (0, vitest_1.expect)(result.error).toMatchInlineSnapshot(`
    [ZodError: [
      {
        "origin": "string",
        "code": "invalid_format",
        "format": "nanoid",
        "pattern": "/^[a-zA-Z0-9_-]{21}$/",
        "path": [],
        "message": "custom error"
      }
    ]]
  `);
});
(0, vitest_1.test)("bad nanoid", () => {
    const nanoid = z.string().nanoid("custom error");
    nanoid.parse("ySh_984wpDUu7IQRrLXAp");
    const result = nanoid.safeParse("invalid nanoid");
    (0, vitest_1.expect)(result).toMatchObject({ success: false });
    (0, vitest_1.expect)(result.error.issues[0].message).toEqual("custom error");
    (0, vitest_1.expect)(result.error).toMatchInlineSnapshot(`
    [ZodError: [
      {
        "origin": "string",
        "code": "invalid_format",
        "format": "nanoid",
        "pattern": "/^[a-zA-Z0-9_-]{21}$/",
        "path": [],
        "message": "custom error"
      }
    ]]
  `);
});
(0, vitest_1.test)("good uuid", () => {
    const uuid = z.string().uuid("custom error");
    const goodUuids = [
        "9491d710-3185-1e06-bea0-6a2f275345e0",
        "9491d710-3185-2e06-bea0-6a2f275345e0",
        "9491d710-3185-3e06-bea0-6a2f275345e0",
        "9491d710-3185-4e06-bea0-6a2f275345e0",
        "9491d710-3185-5e06-bea0-6a2f275345e0",
        "9491d710-3185-5e06-aea0-6a2f275345e0",
        "9491d710-3185-5e06-8ea0-6a2f275345e0",
        "9491d710-3185-5e06-9ea0-6a2f275345e0",
        "00000000-0000-0000-0000-000000000000",
    ];
    for (const goodUuid of goodUuids) {
        const result = uuid.safeParse(goodUuid);
        (0, vitest_1.expect)(result.success).toEqual(true);
    }
});
(0, vitest_1.test)(`bad uuid`, () => {
    const uuid = z.string().uuid("custom error");
    for (const badUuid of [
        "9491d710-3185-0e06-bea0-6a2f275345e0",
        "9491d710-3185-5e06-0ea0-6a2f275345e0",
        "d89e7b01-7598-ed11-9d7a-0022489382fd", // new sequential id
        "b3ce60f8-e8b9-40f5-1150-172ede56ff74", // Variant 0 - RFC 4122: Reserved, NCS backward compatibility
        "92e76bf9-28b3-4730-cd7f-cb6bc51f8c09", // Variant 2 - RFC 4122: Reserved, Microsoft Corporation backward compatibility
        "invalid uuid",
        "9491d710-3185-4e06-bea0-6a2f275345e0X",
        "ffffffff-ffff-ffff-ffff-ffffffffffff",
    ]) {
        const result = uuid.safeParse(badUuid);
        (0, vitest_1.expect)(result).toMatchObject({ success: false });
        (0, vitest_1.expect)(result.error?.issues[0].message).toEqual("custom error");
    }
});
(0, vitest_1.test)("good guid", () => {
    const guid = z.string().guid("custom error");
    for (const goodGuid of [
        "9491d710-3185-4e06-bea0-6a2f275345e0",
        "d89e7b01-7598-ed11-9d7a-0022489382fd", // new sequential id
        "b3ce60f8-e8b9-40f5-1150-172ede56ff74", // Variant 0 - RFC 4122: Reserved, NCS backward compatibility
        "92e76bf9-28b3-4730-cd7f-cb6bc51f8c09", // Variant 2 - RFC 4122: Reserved, Microsoft Corporation backward compatibility
        "00000000-0000-0000-0000-000000000000",
        "ffffffff-ffff-ffff-ffff-ffffffffffff",
    ]) {
        const result = guid.safeParse(goodGuid);
        (0, vitest_1.expect)(result.success).toEqual(true);
    }
});
(0, vitest_1.test)("bad guid", () => {
    const guid = z.string().guid("custom error");
    for (const badGuid of ["9491d710-3185-4e06-bea0-6a2f275345e0X"]) {
        const result = guid.safeParse(badGuid);
        (0, vitest_1.expect)(result).toMatchObject({ success: false });
        (0, vitest_1.expect)(result.error?.issues[0].message).toEqual("custom error");
    }
});
(0, vitest_1.test)("cuid", () => {
    const cuid = z.string().cuid();
    cuid.parse("ckopqwooh000001la8mbi2im9");
    const result = cuid.safeParse("cifjhdsfhsd-invalid-cuid");
    (0, vitest_1.expect)(result).toMatchObject({ success: false });
    (0, vitest_1.expect)(result.error.issues[0].message).toEqual("Invalid cuid");
    (0, vitest_1.expect)(result.error).toMatchInlineSnapshot(`
    [ZodError: [
      {
        "origin": "string",
        "code": "invalid_format",
        "format": "cuid",
        "pattern": "/^[cC][^\\\\s-]{8,}$/",
        "path": [],
        "message": "Invalid cuid"
      }
    ]]
  `);
});
(0, vitest_1.test)("cuid2", () => {
    const cuid2 = z.string().cuid2();
    const validStrings = [
        "a", // short string
        "tz4a98xxat96iws9zmbrgj3a", // normal string
        "kf5vz6ssxe4zjcb409rjgo747tc5qjazgptvotk6", // longer than require("@paralleldrive/cuid2").bigLength
    ];
    for (const s of validStrings) {
        cuid2.parse(s);
    }
    const invalidStrings = [
        "", // empty string
        "tz4a98xxat96iws9zMbrgj3a", // include uppercase
        "tz4a98xxat96iws-zmbrgj3a", // involve symbols
    ];
    const results = invalidStrings.map((s) => cuid2.safeParse(s));
    (0, vitest_1.expect)(results.every((r) => !r.success)).toEqual(true);
    if (!results[0].success) {
        (0, vitest_1.expect)(results[0].error.issues[0].message).toEqual("Invalid cuid2");
    }
});
(0, vitest_1.test)("ulid", () => {
    const ulid = z.string().ulid();
    ulid.parse("01ARZ3NDEKTSV4RRFFQ69G5FAV");
    const result = ulid.safeParse("invalidulid");
    (0, vitest_1.expect)(result).toMatchObject({ success: false });
    const tooLong = "01ARZ3NDEKTSV4RRFFQ69G5FAVA";
    (0, vitest_1.expect)(ulid.safeParse(tooLong)).toMatchObject({ success: false });
    const caseInsensitive = ulid.safeParse("01arZ3nDeKTsV4RRffQ69G5FAV");
    (0, vitest_1.expect)(caseInsensitive.success).toEqual(true);
    (0, vitest_1.expect)(result.error.issues[0].message).toEqual("Invalid ULID");
    (0, vitest_1.expect)(result.error).toMatchInlineSnapshot(`
    [ZodError: [
      {
        "origin": "string",
        "code": "invalid_format",
        "format": "ulid",
        "pattern": "/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/",
        "path": [],
        "message": "Invalid ULID"
      }
    ]]
  `);
});
(0, vitest_1.test)("xid", () => {
    const xid = z.string().xid();
    xid.parse("9m4e2mr0ui3e8a215n4g");
    const result = xid.safeParse("invalidxid");
    (0, vitest_1.expect)(result).toMatchObject({ success: false });
    (0, vitest_1.expect)(result.error.issues[0].message).toEqual("Invalid XID");
    (0, vitest_1.expect)(result.error).toMatchInlineSnapshot(`
    [ZodError: [
      {
        "origin": "string",
        "code": "invalid_format",
        "format": "xid",
        "pattern": "/^[0-9a-vA-V]{20}$/",
        "path": [],
        "message": "Invalid XID"
      }
    ]]
  `);
});
(0, vitest_1.test)("ksuid", () => {
    const ksuid = z.string().ksuid();
    ksuid.parse("0o0t9hkGxgFLtd3lmJ4TSTeY0Vb");
    const result = ksuid.safeParse("invalidksuid");
    (0, vitest_1.expect)(result).toMatchObject({ success: false });
    const tooLong = "0o0t9hkGxgFLtd3lmJ4TSTeY0VbA";
    (0, vitest_1.expect)(ksuid.safeParse(tooLong)).toMatchObject({ success: false });
    (0, vitest_1.expect)(result.error.issues).toMatchInlineSnapshot(`
    [
      {
        "code": "invalid_format",
        "format": "ksuid",
        "message": "Invalid KSUID",
        "origin": "string",
        "path": [],
        "pattern": "/^[A-Za-z0-9]{27}$/",
      },
    ]
  `);
});
(0, vitest_1.test)("regex", () => {
    z.string()
        .regex(/^moo+$/)
        .parse("mooooo");
    (0, vitest_1.expect)(() => z.string().uuid().parse("purr")).toThrow();
});
(0, vitest_1.test)("regexp error message", () => {
    const result = z
        .string()
        .regex(/^moo+$/)
        .safeParse("boooo");
    (0, vitest_1.expect)(result.error.issues).toMatchInlineSnapshot(`
    [
      {
        "code": "invalid_format",
        "format": "regex",
        "message": "Invalid string: must match pattern /^moo+$/",
        "origin": "string",
        "path": [],
        "pattern": "/^moo+$/",
      },
    ]
  `);
    (0, vitest_1.expect)(() => z.string().uuid().parse("purr")).toThrow();
});
(0, vitest_1.test)("regexp error custom message", () => {
    const result = z
        .string()
        .regex(/^moo+$/, { message: "Custom error message" })
        .safeParse("boooo");
    (0, vitest_1.expect)(result.error.issues).toMatchInlineSnapshot(`
    [
      {
        "code": "invalid_format",
        "format": "regex",
        "message": "Custom error message",
        "origin": "string",
        "path": [],
        "pattern": "/^moo+$/",
      },
    ]
  `);
    (0, vitest_1.expect)(() => z.string().uuid().parse("purr")).toThrow();
});
(0, vitest_1.test)("regex lastIndex reset", () => {
    const schema = z.string().regex(/^\d+$/g);
    (0, vitest_1.expect)(schema.safeParse("123").success).toEqual(true);
    (0, vitest_1.expect)(schema.safeParse("123").success).toEqual(true);
    (0, vitest_1.expect)(schema.safeParse("123").success).toEqual(true);
    (0, vitest_1.expect)(schema.safeParse("123").success).toEqual(true);
    (0, vitest_1.expect)(schema.safeParse("123").success).toEqual(true);
});
(0, vitest_1.test)("format", () => {
    (0, vitest_1.expect)(z.string().email().format).toEqual("email");
    (0, vitest_1.expect)(z.string().url().format).toEqual("url");
    (0, vitest_1.expect)(z.string().jwt().format).toEqual("jwt");
    (0, vitest_1.expect)(z.string().emoji().format).toEqual("emoji");
    (0, vitest_1.expect)(z.string().guid().format).toEqual("guid");
    (0, vitest_1.expect)(z.string().uuid().format).toEqual("uuid");
    (0, vitest_1.expect)(z.string().uuidv4().format).toEqual("uuid");
    (0, vitest_1.expect)(z.string().uuidv6().format).toEqual("uuid");
    (0, vitest_1.expect)(z.string().uuidv7().format).toEqual("uuid");
    (0, vitest_1.expect)(z.string().nanoid().format).toEqual("nanoid");
    (0, vitest_1.expect)(z.string().guid().format).toEqual("guid");
    (0, vitest_1.expect)(z.string().cuid().format).toEqual("cuid");
    (0, vitest_1.expect)(z.string().cuid2().format).toEqual("cuid2");
    (0, vitest_1.expect)(z.string().ulid().format).toEqual("ulid");
    (0, vitest_1.expect)(z.string().base64().format).toEqual("base64");
    // expect(z.string().jsonString().format).toEqual("json_string");
    // expect(z.string().json().format).toEqual("json_string");
    (0, vitest_1.expect)(z.string().xid().format).toEqual("xid");
    (0, vitest_1.expect)(z.string().ksuid().format).toEqual("ksuid");
    // expect(z.string().ip().format).toEqual("ip");
    (0, vitest_1.expect)(z.string().ipv4().format).toEqual("ipv4");
    (0, vitest_1.expect)(z.string().ipv6().format).toEqual("ipv6");
    (0, vitest_1.expect)(z.string().e164().format).toEqual("e164");
    (0, vitest_1.expect)(z.string().datetime().format).toEqual("datetime");
    (0, vitest_1.expect)(z.string().date().format).toEqual("date");
    (0, vitest_1.expect)(z.string().time().format).toEqual("time");
    (0, vitest_1.expect)(z.string().duration().format).toEqual("duration");
});
(0, vitest_1.test)("min max getters", () => {
    (0, vitest_1.expect)(z.string().min(5).minLength).toEqual(5);
    (0, vitest_1.expect)(z.string().min(5).min(10).minLength).toEqual(10);
    (0, vitest_1.expect)(z.string().minLength).toEqual(null);
    (0, vitest_1.expect)(z.string().max(5).maxLength).toEqual(5);
    (0, vitest_1.expect)(z.string().max(5).max(1).maxLength).toEqual(1);
    (0, vitest_1.expect)(z.string().max(5).max(10).maxLength).toEqual(5);
    (0, vitest_1.expect)(z.string().maxLength).toEqual(null);
});
(0, vitest_1.test)("trim", () => {
    (0, vitest_1.expect)(z.string().trim().min(2).parse(" 12 ")).toEqual("12");
    // ordering of methods is respected
    (0, vitest_1.expect)(z.string().min(2).trim().parse(" 1 ")).toEqual("1");
    (0, vitest_1.expect)(() => z.string().trim().min(2).parse(" 1 ")).toThrow();
});
(0, vitest_1.test)("lowerCase", () => {
    (0, vitest_1.expect)(z.string().toLowerCase().parse("ASDF")).toEqual("asdf");
    (0, vitest_1.expect)(z.string().toUpperCase().parse("asdf")).toEqual("ASDF");
});
(0, vitest_1.test)("datetime parsing", () => {
    const datetime = z.string().datetime();
    datetime.parse("1970-01-01T00:00:00.000Z");
    datetime.parse("2022-10-13T09:52:31.816Z");
    datetime.parse("2022-10-13T09:52:31.8162314Z");
    datetime.parse("1970-01-01T00:00:00Z");
    datetime.parse("2022-10-13T09:52:31Z");
    (0, vitest_1.expect)(() => datetime.parse("")).toThrow();
    (0, vitest_1.expect)(() => datetime.parse("foo")).toThrow();
    (0, vitest_1.expect)(() => datetime.parse("2020-10-14")).toThrow();
    (0, vitest_1.expect)(() => datetime.parse("T18:45:12.123")).toThrow();
    (0, vitest_1.expect)(() => datetime.parse("2020-10-14T17:42:29+00:00")).toThrow();
    const datetimeNoMs = z.string().datetime({ precision: 0 });
    datetimeNoMs.parse("1970-01-01T00:00:00Z");
    datetimeNoMs.parse("2022-10-13T09:52:31Z");
    (0, vitest_1.expect)(() => datetimeNoMs.parse("tuna")).toThrow();
    (0, vitest_1.expect)(() => datetimeNoMs.parse("1970-01-01T00:00:00.000Z")).toThrow();
    (0, vitest_1.expect)(() => datetimeNoMs.parse("1970-01-01T00:00:00.Z")).toThrow();
    (0, vitest_1.expect)(() => datetimeNoMs.parse("2022-10-13T09:52:31.816Z")).toThrow();
    const datetime3Ms = z.string().datetime({ precision: 3 });
    datetime3Ms.parse("1970-01-01T00:00:00.000Z");
    datetime3Ms.parse("2022-10-13T09:52:31.123Z");
    (0, vitest_1.expect)(() => datetime3Ms.parse("tuna")).toThrow();
    (0, vitest_1.expect)(() => datetime3Ms.parse("1970-01-01T00:00:00.1Z")).toThrow();
    (0, vitest_1.expect)(() => datetime3Ms.parse("1970-01-01T00:00:00.12Z")).toThrow();
    (0, vitest_1.expect)(() => datetime3Ms.parse("2022-10-13T09:52:31Z")).toThrow();
    const datetimeOffset = z.string().datetime({ offset: true });
    datetimeOffset.parse("1970-01-01T00:00:00.000Z");
    datetimeOffset.parse("2022-10-13T09:52:31.816234134Z");
    datetimeOffset.parse("1970-01-01T00:00:00Z");
    datetimeOffset.parse("2022-10-13T09:52:31.4Z");
    datetimeOffset.parse("2020-10-14T17:42:29+00:00");
    datetimeOffset.parse("2020-10-14T17:42:29+03:15");
    datetimeOffset.parse("2020-10-14T17:42:29+0315");
    (0, vitest_1.expect)(() => datetimeOffset.parse("2020-10-14T17:42:29+03"));
    (0, vitest_1.expect)(() => datetimeOffset.parse("tuna")).toThrow();
    (0, vitest_1.expect)(() => datetimeOffset.parse("2022-10-13T09:52:31.Z")).toThrow();
    const datetimeOffsetNoMs = z.string().datetime({ offset: true, precision: 0 });
    datetimeOffsetNoMs.parse("1970-01-01T00:00:00Z");
    datetimeOffsetNoMs.parse("2022-10-13T09:52:31Z");
    datetimeOffsetNoMs.parse("2020-10-14T17:42:29+00:00");
    datetimeOffsetNoMs.parse("2020-10-14T17:42:29+0000");
    (0, vitest_1.expect)(() => datetimeOffsetNoMs.parse("2020-10-14T17:42:29+00")).toThrow();
    (0, vitest_1.expect)(() => datetimeOffsetNoMs.parse("tuna")).toThrow();
    (0, vitest_1.expect)(() => datetimeOffsetNoMs.parse("1970-01-01T00:00:00.000Z")).toThrow();
    (0, vitest_1.expect)(() => datetimeOffsetNoMs.parse("1970-01-01T00:00:00.Z")).toThrow();
    (0, vitest_1.expect)(() => datetimeOffsetNoMs.parse("2022-10-13T09:52:31.816Z")).toThrow();
    (0, vitest_1.expect)(() => datetimeOffsetNoMs.parse("2020-10-14T17:42:29.124+00:00")).toThrow();
    const datetimeOffset4Ms = z.string().datetime({ offset: true, precision: 4 });
    datetimeOffset4Ms.parse("1970-01-01T00:00:00.1234Z");
    datetimeOffset4Ms.parse("2020-10-14T17:42:29.1234+00:00");
    datetimeOffset4Ms.parse("2020-10-14T17:42:29.1234+0000");
    (0, vitest_1.expect)(() => datetimeOffset4Ms.parse("2020-10-14T17:42:29.1234+00")).toThrow();
    (0, vitest_1.expect)(() => datetimeOffset4Ms.parse("tuna")).toThrow();
    (0, vitest_1.expect)(() => datetimeOffset4Ms.parse("1970-01-01T00:00:00.123Z")).toThrow();
    (0, vitest_1.expect)(() => datetimeOffset4Ms.parse("2020-10-14T17:42:29.124+00:00")).toThrow();
});
(0, vitest_1.test)("date parsing", () => {
    const date = z.string().date();
    date.parse("1970-01-01");
    date.parse("2022-01-31");
    date.parse("2022-03-31");
    date.parse("2022-04-30");
    date.parse("2022-05-31");
    date.parse("2022-06-30");
    date.parse("2022-07-31");
    date.parse("2022-08-31");
    date.parse("2022-09-30");
    date.parse("2022-10-31");
    date.parse("2022-11-30");
    date.parse("2022-12-31");
    date.parse("2000-02-29");
    date.parse("2400-02-29");
    (0, vitest_1.expect)(() => date.parse("2022-02-29")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2100-02-29")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2200-02-29")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2300-02-29")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2500-02-29")).toThrow();
    (0, vitest_1.expect)(() => date.parse("")).toThrow();
    (0, vitest_1.expect)(() => date.parse("foo")).toThrow();
    (0, vitest_1.expect)(() => date.parse("200-01-01")).toThrow();
    (0, vitest_1.expect)(() => date.parse("20000-01-01")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-0-01")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-011-01")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-01-0")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-01-011")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000/01/01")).toThrow();
    (0, vitest_1.expect)(() => date.parse("01-01-2022")).toThrow();
    (0, vitest_1.expect)(() => date.parse("01/01/2022")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-01-01 00:00:00Z")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2020-10-14T17:42:29+00:00")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2020-10-14T17:42:29Z")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2020-10-14T17:42:29")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2020-10-14T17:42:29.123Z")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-00-12")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-12-00")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-01-32")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-13-01")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-21-01")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-02-30")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-02-31")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-04-31")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-06-31")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-09-31")).toThrow();
    (0, vitest_1.expect)(() => date.parse("2000-11-31")).toThrow();
});
(0, vitest_1.test)("time parsing", () => {
    const time = z.string().time();
    time.parse("00:00:00");
    time.parse("23:00:00");
    time.parse("00:59:00");
    time.parse("00:00:59");
    time.parse("23:59:59");
    time.parse("09:52:31");
    time.parse("23:59:59.9999999");
    (0, vitest_1.expect)(() => time.parse("")).toThrow();
    (0, vitest_1.expect)(() => time.parse("foo")).toThrow();
    (0, vitest_1.expect)(() => time.parse("00:00:00Z")).toThrow();
    (0, vitest_1.expect)(() => time.parse("0:00:00")).toThrow();
    (0, vitest_1.expect)(() => time.parse("00:0:00")).toThrow();
    (0, vitest_1.expect)(() => time.parse("00:00:0")).toThrow();
    (0, vitest_1.expect)(() => time.parse("00:00:00.000+00:00")).toThrow();
    (0, vitest_1.expect)(() => time.parse("24:00:00")).toThrow();
    (0, vitest_1.expect)(() => time.parse("00:60:00")).toThrow();
    (0, vitest_1.expect)(() => time.parse("00:00:60")).toThrow();
    (0, vitest_1.expect)(() => time.parse("24:60:60")).toThrow();
    const time2 = z.string().time({ precision: 2 });
    time2.parse("00:00:00.00");
    time2.parse("09:52:31.12");
    time2.parse("23:59:59.99");
    (0, vitest_1.expect)(() => time2.parse("")).toThrow();
    (0, vitest_1.expect)(() => time2.parse("foo")).toThrow();
    (0, vitest_1.expect)(() => time2.parse("00:00:00")).toThrow();
    (0, vitest_1.expect)(() => time2.parse("00:00:00.00Z")).toThrow();
    (0, vitest_1.expect)(() => time2.parse("00:00:00.0")).toThrow();
    (0, vitest_1.expect)(() => time2.parse("00:00:00.000")).toThrow();
    (0, vitest_1.expect)(() => time2.parse("00:00:00.00+00:00")).toThrow();
});
(0, vitest_1.test)("duration", () => {
    const duration = z.string().duration();
    const validDurations = [
        "P3Y6M4DT12H30M5S",
        "P2Y9M3DT12H31M8.001S",
        // "+P3Y6M4DT12H30M5S",
        // "-PT0.001S",
        // "+PT0.001S",
        "PT0,001S",
        "PT12H30M5S",
        // "-P2M1D",
        // "P-2M-1D",
        // "-P5DT10H",
        // "P-5DT-10H",
        "P1Y",
        "P2MT30M",
        "PT6H",
        "P5W",
        // "P0.5Y",
        // "P0,5Y",
        // "P42YT7.004M",
    ];
    const invalidDurations = [
        "foo bar",
        "",
        " ",
        "P",
        "PT",
        "P1Y2MT",
        "T1H",
        "P0.5Y1D",
        "P0,5Y6M",
        "P1YT",
        "P-2M-1D",
        "P-5DT-10H",
        "P1W2D",
        "-P1D",
    ];
    for (const val of validDurations) {
        const result = duration.safeParse(val);
        if (!result.success) {
            throw Error(`Valid duration could not be parsed: ${val}`);
        }
    }
    for (const val of invalidDurations) {
        const result = duration.safeParse(val);
        if (result.success) {
            throw Error(`Invalid duration was successful parsed: ${val}`);
        }
        (0, vitest_1.expect)(result.error.issues[0].message).toEqual("Invalid ISO duration");
    }
});
// test("IP validation", () => {
//   const ipSchema = z.string().ip();
//   // General IP validation (accepts both v4 and v6)
//   expect(ipSchema.safeParse("************").success).toBe(true);
//   expect(ipSchema.safeParse("0.0.0.0").success).toBe(true);
//   expect(ipSchema.safeParse("*************").success).toBe(true);
//   expect(ipSchema.safeParse("1e5e:e6c8:daac:514b:114b:e360:d8c0:682c").success).toBe(true);
//   expect(ipSchema.safeParse("9d4:c956:420f:5788:4339:9b3b:2418:75c3").success).toBe(true);
//   expect(ipSchema.safeParse("a6ea::2454:a5ce:*************").success).toBe(true);
//   expect(ipSchema.safeParse("474f:4c83::4e40:a47:ff95:0cda").success).toBe(true);
//   expect(ipSchema.safeParse("d329:0:25b4:db47:a9d1:0:4926:0000").success).toBe(true);
//   expect(ipSchema.safeParse("e48:10fb:1499:3e28:e4b6:dea5:4692:912c").success).toBe(true);
//   expect(ipSchema.safeParse("d329:1be4:25b4:db47:a9d1:dc71:4926:992c:14af").success).toBe(false);
//   expect(ipSchema.safeParse("d5e7:7214:2b78::3906:85e6:53cc:709:32ba").success).toBe(false);
//   expect(ipSchema.safeParse("8f69::c757:395e:976e::3441").success).toBe(false);
//   expect(ipSchema.safeParse("54cb::473f:d516:0.255.256.22").success).toBe(false);
//   expect(ipSchema.safeParse("54cb::473f:d516:192.168.1").success).toBe(false);
//   expect(ipSchema.safeParse("256.0.4.4").success).toBe(false);
//   expect(ipSchema.safeParse("-1.0.555.4").success).toBe(false);
//   expect(ipSchema.safeParse("0.0.0.0.0").success).toBe(false);
//   expect(ipSchema.safeParse("1.1.1").success).toBe(false);
// });
(0, vitest_1.test)("IPv4 validation", () => {
    const ipv4 = z.string().ipv4();
    // Valid IPv4 addresses
    (0, vitest_1.expect)(ipv4.safeParse("************").success).toBe(true);
    (0, vitest_1.expect)(ipv4.safeParse("0.0.0.0").success).toBe(true);
    (0, vitest_1.expect)(ipv4.safeParse("*************").success).toBe(true);
    (0, vitest_1.expect)(ipv4.safeParse("***********").success).toBe(true);
    (0, vitest_1.expect)(ipv4.safeParse("***************").success).toBe(true);
    (0, vitest_1.expect)(ipv4.safeParse("*******").success).toBe(true);
    // Invalid IPv4 addresses
    (0, vitest_1.expect)(ipv4.safeParse("256.0.4.4").success).toBe(false);
    (0, vitest_1.expect)(ipv4.safeParse("-1.0.555.4").success).toBe(false);
    (0, vitest_1.expect)(ipv4.safeParse("0.0.0.0.0").success).toBe(false);
    (0, vitest_1.expect)(ipv4.safeParse("1.1.1").success).toBe(false);
    (0, vitest_1.expect)(ipv4.safeParse("1e5e:e6c8:daac:514b:114b:e360:d8c0:682c").success).toBe(false);
    (0, vitest_1.expect)(ipv4.safeParse("a6ea::2454:a5ce:*************").success).toBe(false);
    (0, vitest_1.expect)(ipv4.safeParse("not an ip").success).toBe(false);
    (0, vitest_1.expect)(ipv4.safeParse("1.2.3").success).toBe(false);
    (0, vitest_1.expect)(ipv4.safeParse("*******.5").success).toBe(false);
    (0, vitest_1.expect)(ipv4.safeParse("1.2.3.256").success).toBe(false);
    // Test specific error
    (0, vitest_1.expect)(() => ipv4.parse("6097:adfa:6f0b:220d:db08:5021:6191:7990")).toThrow();
});
(0, vitest_1.test)("IPv6 validation", () => {
    const ipv6 = z.string().ipv6();
    // Valid IPv6 addresses
    (0, vitest_1.expect)(ipv6.safeParse("1e5e:e6c8:daac:514b:114b:e360:d8c0:682c").success).toBe(true);
    (0, vitest_1.expect)(ipv6.safeParse("9d4:c956:420f:5788:4339:9b3b:2418:75c3").success).toBe(true);
    (0, vitest_1.expect)(ipv6.safeParse("a6ea::2454:a5ce:*************").success).toBe(true);
    (0, vitest_1.expect)(ipv6.safeParse("474f:4c83::4e40:a47:ff95:0cda").success).toBe(true);
    (0, vitest_1.expect)(ipv6.safeParse("d329:0:25b4:db47:a9d1:0:4926:0000").success).toBe(true);
    (0, vitest_1.expect)(ipv6.safeParse("e48:10fb:1499:3e28:e4b6:dea5:4692:912c").success).toBe(true);
    (0, vitest_1.expect)(ipv6.safeParse("::1").success).toBe(true);
    (0, vitest_1.expect)(ipv6.safeParse("2001:db8::").success).toBe(true);
    (0, vitest_1.expect)(ipv6.safeParse("2001:0db8:85a3:0000:0000:8a2e:0370:7334").success).toBe(true);
    (0, vitest_1.expect)(ipv6.safeParse("2001:db8::***********").success).toBe(true);
    (0, vitest_1.expect)(ipv6.safeParse("::ffff:***********").success).toBe(true);
    (0, vitest_1.expect)(ipv6.safeParse("::ffff:c000:0280").success).toBe(true); // IPv4-mapped IPv6 address
    (0, vitest_1.expect)(ipv6.safeParse("64:ff9b::***********").success).toBe(true); // IPv4/IPv6 translation
    // Invalid IPv6 addresses
    (0, vitest_1.expect)(ipv6.safeParse("d329:1be4:25b4:db47:a9d1:dc71:4926:992c:14af").success).toBe(false);
    (0, vitest_1.expect)(ipv6.safeParse("d5e7:7214:2b78::3906:85e6:53cc:709:32ba").success).toBe(false);
    (0, vitest_1.expect)(ipv6.safeParse("8f69::c757:395e:976e::3441").success).toBe(false);
    (0, vitest_1.expect)(ipv6.safeParse("54cb::473f:d516:0.255.256.22").success).toBe(false);
    (0, vitest_1.expect)(ipv6.safeParse("54cb::473f:d516:192.168.1").success).toBe(false);
    (0, vitest_1.expect)(ipv6.safeParse("************").success).toBe(false);
    (0, vitest_1.expect)(ipv6.safeParse("not an ip").success).toBe(false);
    (0, vitest_1.expect)(ipv6.safeParse("g123::1234:5678").success).toBe(false);
    // Test specific error
    (0, vitest_1.expect)(() => ipv6.parse("************")).toThrow();
});
(0, vitest_1.test)("CIDR v4 validation", () => {
    const cidrV4 = z.string().cidrv4();
    // Valid CIDR v4 addresses
    (0, vitest_1.expect)(cidrV4.safeParse("***********/24").success).toBe(true);
    (0, vitest_1.expect)(cidrV4.safeParse("10.0.0.0/8").success).toBe(true);
    (0, vitest_1.expect)(cidrV4.safeParse("**********/12").success).toBe(true);
    (0, vitest_1.expect)(cidrV4.safeParse("0.0.0.0/0").success).toBe(true);
    (0, vitest_1.expect)(cidrV4.safeParse("***************/32").success).toBe(true);
    // Invalid CIDR v4 addresses
    (0, vitest_1.expect)(cidrV4.safeParse("***********").success).toBe(false); // Missing prefix
    (0, vitest_1.expect)(cidrV4.safeParse("***********/33").success).toBe(false); // Invalid prefix length
    (0, vitest_1.expect)(cidrV4.safeParse("256.0.0.0/24").success).toBe(false); // Invalid IP
    (0, vitest_1.expect)(cidrV4.safeParse("***********/-1").success).toBe(false); // Negative prefix length
    (0, vitest_1.expect)(cidrV4.safeParse("not a cidr").success).toBe(false); // Invalid format
});
(0, vitest_1.test)("CIDR v6 validation", () => {
    const cidrV6 = z.string().cidrv6();
    // Valid CIDR v6 addresses
    (0, vitest_1.expect)(cidrV6.safeParse("2001:db8::/32").success).toBe(true);
    (0, vitest_1.expect)(cidrV6.safeParse("::/0").success).toBe(true);
    (0, vitest_1.expect)(cidrV6.safeParse("fe80::/10").success).toBe(true);
    (0, vitest_1.expect)(cidrV6.safeParse("::1/128").success).toBe(true);
    (0, vitest_1.expect)(cidrV6.safeParse("2001:0db8:85a3::/64").success).toBe(true);
    // Invalid CIDR v6 addresses
    (0, vitest_1.expect)(cidrV6.safeParse("2001:db8::").success).toBe(false); // Missing prefix
    (0, vitest_1.expect)(cidrV6.safeParse("2001:db8::/129").success).toBe(false); // Invalid prefix length
    (0, vitest_1.expect)(cidrV6.safeParse("2001:db8::/abc").success).toBe(false); // Invalid prefix format
    (0, vitest_1.expect)(cidrV6.safeParse("not a cidr").success).toBe(false); // Invalid format
    (0, vitest_1.expect)(cidrV6.safeParse("***********/24").success).toBe(false); // IPv4 CIDR in v6 validation
});
(0, vitest_1.test)("E.164 validation", () => {
    const e164Number = z.string().e164();
    (0, vitest_1.expect)(e164Number.safeParse("+1555555").success).toBe(true);
    const validE164Numbers = [
        "+1555555", // min-length (7 digits + '+')
        "+15555555",
        "+155555555",
        "+1555555555",
        "+15555555555",
        "+155555555555",
        "+1555555555555",
        "+15555555555555",
        "+155555555555555",
        "+105555555555555",
        "+100555555555555", // max-length (15 digits + '+')
    ];
    const invalidE164Numbers = [
        "", // empty
        "+", // only plus sign
        "-", // wrong sign
        " 555555555", // starts with space
        "555555555", // missing plus sign
        "+1 555 555 555", // space after plus sign
        "+1555 555 555", // space between numbers
        "+1555+555", // multiple plus signs
        "+1555555555555555", // too long
        "+115abc55", // non numeric characters in number part
        "+1555555 ", // space after number
    ];
    (0, vitest_1.expect)(validE164Numbers.every((number) => e164Number.safeParse(number).success)).toBe(true);
    (0, vitest_1.expect)(invalidE164Numbers.every((number) => e164Number.safeParse(number).success === false)).toBe(true);
});
