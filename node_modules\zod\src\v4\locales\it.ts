import type { $ZodStringFormats } from "../core/checks.js";
import type * as errors from "../core/errors.js";
import * as util from "../core/util.js";

const Sizable: Record<string, { unit: string; verb: string }> = {
  string: { unit: "caratteri", verb: "avere" },
  file: { unit: "byte", verb: "avere" },
  array: { unit: "elementi", verb: "avere" },
  set: { unit: "elementi", verb: "avere" },
};

function getSizing(origin: string): { unit: string; verb: string } | null {
  return Sizable[origin] ?? null;
}

export const parsedType = (data: any): string => {
  const t = typeof data;

  switch (t) {
    case "number": {
      return Number.isNaN(data) ? "NaN" : "numero";
    }
    case "object": {
      if (Array.isArray(data)) {
        return "vettore";
      }
      if (data === null) {
        return "null";
      }

      if (Object.getPrototypeOf(data) !== Object.prototype && data.constructor) {
        return data.constructor.name;
      }
    }
  }
  return t;
};

const Nouns: {
  [k in $ZodStringFormats | (string & {})]?: string;
} = {
  regex: "input",
  email: "indirizzo email",
  url: "URL",
  emoji: "emoji",
  uuid: "UUID",
  uuidv4: "UUIDv4",
  uuidv6: "UUIDv6",
  nanoid: "nanoid",
  guid: "GUID",
  cuid: "cuid",
  cuid2: "cuid2",
  ulid: "ULID",
  xid: "XID",
  ksuid: "KSUID",
  datetime: "data e ora ISO",
  date: "data ISO",
  time: "ora ISO",
  duration: "durata ISO",
  ipv4: "indirizzo IPv4",
  ipv6: "indirizzo IPv6",
  cidrv4: "intervallo IPv4",
  cidrv6: "intervallo IPv6",
  base64: "stringa codificata in base64",
  base64url: "URL codificata in base64",
  json_string: "stringa JSON",
  e164: "numero E.164",
  jwt: "JWT",
  template_literal: "input",
};

const error: errors.$ZodErrorMap = (issue) => {
  switch (issue.code) {
    case "invalid_type":
      return `Input non valido: atteso ${issue.expected}, ricevuto ${parsedType(issue.input)}`;
    // return `Input non valido: atteso ${issue.expected}, ricevuto ${util.getParsedType(issue.input)}`;
    case "invalid_value":
      if (issue.values.length === 1) return `Input non valido: atteso ${util.stringifyPrimitive(issue.values[0])}`;
      return `Opzione non valida: atteso uno tra ${util.joinValues(issue.values, "|")}`;
    case "too_big": {
      const adj = issue.inclusive ? "<=" : "<";
      const sizing = getSizing(issue.origin);
      if (sizing)
        return `Troppo grande: ${issue.origin ?? "valore"} deve avere ${adj}${issue.maximum.toString()} ${sizing.unit ?? "elementi"}`;
      return `Troppo grande: ${issue.origin ?? "valore"} deve essere ${adj}${issue.maximum.toString()}`;
    }
    case "too_small": {
      const adj = issue.inclusive ? ">=" : ">";
      const sizing = getSizing(issue.origin);
      if (sizing) {
        return `Troppo piccolo: ${issue.origin} deve avere ${adj}${issue.minimum.toString()} ${sizing.unit}`;
      }

      return `Troppo piccolo: ${issue.origin} deve essere ${adj}${issue.minimum.toString()}`;
    }
    case "invalid_format": {
      const _issue = issue as errors.$ZodStringFormatIssues;
      if (_issue.format === "starts_with") return `Stringa non valida: deve iniziare con "${_issue.prefix}"`;
      if (_issue.format === "ends_with") return `Stringa non valida: deve terminare con "${_issue.suffix}"`;
      if (_issue.format === "includes") return `Stringa non valida: deve includere "${_issue.includes}"`;
      if (_issue.format === "regex") return `Stringa non valida: deve corrispondere al pattern ${_issue.pattern}`;
      return `Invalid ${Nouns[_issue.format] ?? issue.format}`;
    }
    case "not_multiple_of":
      return `Numero non valido: deve essere un multiplo di ${issue.divisor}`;
    case "unrecognized_keys":
      return `Chiav${issue.keys.length > 1 ? "i" : "e"} non riconosciut${issue.keys.length > 1 ? "e" : "a"}: ${util.joinValues(issue.keys, ", ")}`;
    case "invalid_key":
      return `Chiave non valida in ${issue.origin}`;
    case "invalid_union":
      return "Input non valido";
    case "invalid_element":
      return `Valore non valido in ${issue.origin}`;
    default:
      return `Input non valido`;
  }
};

export { error };

export default function (): { localeError: errors.$ZodErrorMap } {
  return {
    localeError: error,
  };
}
