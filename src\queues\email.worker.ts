import { Worker } from "bullmq";
import { emailVerification, orderStatusUpdate, cancelOrder, placedOrder } from "../services/resend.js";
import redis from "../services/redis.js";

const worker = new Worker("email-queue", async (job) => {
  const { type, payload } = job.data;
  console.log("han worker kaam kar raha ha.")
  switch (type) {
    case "verify-email":
      console.log("yes working")
     const emailResponse =  await emailVerification(payload.userName, payload.email, payload.otp);
     console.log(emailResponse)
      break;

    case "order-placed":
      await placedOrder(payload.userName, payload.email, payload.message, payload.orderId);
      break;

    case "order-status-update":
      await orderStatusUpdate(payload.userName, payload.email, payload.message, payload.orderId);
      break;

    case "order-cancel":
      await cancelOrder(payload.userName, payload.email, payload.message, payload.orderId);
      break;

    default:
      console.log("❌ Unknown email type");
  }
}, { connection: redis });

worker.on("completed", (job) => {
  console.log(`✅ Email job [${job.name}] completed`);
});

worker.on("failed", (job, err) => {
  console.error(`❌ Email job failed:`, err.message);
});
