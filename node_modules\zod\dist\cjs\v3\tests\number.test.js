"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
// @ts-ignore TS6133
const vitest_1 = require("vitest");
const z = __importStar(require("zod/v3"));
const gtFive = z.number().gt(5);
const gteFive = z.number().gte(-5).gte(5);
const minFive = z.number().min(0).min(5);
const ltFive = z.number().lte(10).lt(5);
const lteFive = z.number().lte(5);
const maxFive = z.number().max(10).max(5);
const intNum = z.number().int();
const positive = z.number().positive();
const negative = z.number().negative();
const nonpositive = z.number().nonpositive();
const nonnegative = z.number().nonnegative();
const multipleOfFive = z.number().multipleOf(5);
const multipleOfNegativeFive = z.number().multipleOf(-5);
const finite = z.number().finite();
const safe = z.number().safe();
const stepPointOne = z.number().step(0.1);
const stepPointZeroZeroZeroOne = z.number().step(0.0001);
const stepSixPointFour = z.number().step(6.4);
(0, vitest_1.test)("passing validations", () => {
    z.number().parse(1);
    z.number().parse(1.5);
    z.number().parse(0);
    z.number().parse(-1.5);
    z.number().parse(-1);
    z.number().parse(Number.POSITIVE_INFINITY);
    z.number().parse(Number.NEGATIVE_INFINITY);
    gtFive.parse(6);
    gtFive.parse(Number.POSITIVE_INFINITY);
    gteFive.parse(5);
    gteFive.parse(Number.POSITIVE_INFINITY);
    minFive.parse(5);
    minFive.parse(Number.POSITIVE_INFINITY);
    ltFive.parse(4);
    ltFive.parse(Number.NEGATIVE_INFINITY);
    lteFive.parse(5);
    lteFive.parse(Number.NEGATIVE_INFINITY);
    maxFive.parse(5);
    maxFive.parse(Number.NEGATIVE_INFINITY);
    intNum.parse(4);
    positive.parse(1);
    positive.parse(Number.POSITIVE_INFINITY);
    negative.parse(-1);
    negative.parse(Number.NEGATIVE_INFINITY);
    nonpositive.parse(0);
    nonpositive.parse(-1);
    nonpositive.parse(Number.NEGATIVE_INFINITY);
    nonnegative.parse(0);
    nonnegative.parse(1);
    nonnegative.parse(Number.POSITIVE_INFINITY);
    multipleOfFive.parse(15);
    multipleOfFive.parse(-15);
    multipleOfNegativeFive.parse(-15);
    multipleOfNegativeFive.parse(15);
    finite.parse(123);
    safe.parse(Number.MIN_SAFE_INTEGER);
    safe.parse(Number.MAX_SAFE_INTEGER);
    stepPointOne.parse(6);
    stepPointOne.parse(6.1);
    stepPointOne.parse(6.1);
    stepSixPointFour.parse(12.8);
    stepPointZeroZeroZeroOne.parse(3.01);
});
(0, vitest_1.test)("failing validations", () => {
    (0, vitest_1.expect)(() => ltFive.parse(5)).toThrow();
    (0, vitest_1.expect)(() => lteFive.parse(6)).toThrow();
    (0, vitest_1.expect)(() => maxFive.parse(6)).toThrow();
    (0, vitest_1.expect)(() => gtFive.parse(5)).toThrow();
    (0, vitest_1.expect)(() => gteFive.parse(4)).toThrow();
    (0, vitest_1.expect)(() => minFive.parse(4)).toThrow();
    (0, vitest_1.expect)(() => intNum.parse(3.14)).toThrow();
    (0, vitest_1.expect)(() => positive.parse(0)).toThrow();
    (0, vitest_1.expect)(() => positive.parse(-1)).toThrow();
    (0, vitest_1.expect)(() => negative.parse(0)).toThrow();
    (0, vitest_1.expect)(() => negative.parse(1)).toThrow();
    (0, vitest_1.expect)(() => nonpositive.parse(1)).toThrow();
    (0, vitest_1.expect)(() => nonnegative.parse(-1)).toThrow();
    (0, vitest_1.expect)(() => multipleOfFive.parse(7.5)).toThrow();
    (0, vitest_1.expect)(() => multipleOfFive.parse(-7.5)).toThrow();
    (0, vitest_1.expect)(() => multipleOfNegativeFive.parse(-7.5)).toThrow();
    (0, vitest_1.expect)(() => multipleOfNegativeFive.parse(7.5)).toThrow();
    (0, vitest_1.expect)(() => finite.parse(Number.POSITIVE_INFINITY)).toThrow();
    (0, vitest_1.expect)(() => finite.parse(Number.NEGATIVE_INFINITY)).toThrow();
    (0, vitest_1.expect)(() => safe.parse(Number.MIN_SAFE_INTEGER - 1)).toThrow();
    (0, vitest_1.expect)(() => safe.parse(Number.MAX_SAFE_INTEGER + 1)).toThrow();
    (0, vitest_1.expect)(() => stepPointOne.parse(6.11)).toThrow();
    (0, vitest_1.expect)(() => stepPointOne.parse(6.1000000001)).toThrow();
    (0, vitest_1.expect)(() => stepSixPointFour.parse(6.41)).toThrow();
});
(0, vitest_1.test)("parse NaN", () => {
    (0, vitest_1.expect)(() => z.number().parse(Number.NaN)).toThrow();
});
(0, vitest_1.test)("min max getters", () => {
    (0, vitest_1.expect)(z.number().minValue).toBeNull;
    (0, vitest_1.expect)(ltFive.minValue).toBeNull;
    (0, vitest_1.expect)(lteFive.minValue).toBeNull;
    (0, vitest_1.expect)(maxFive.minValue).toBeNull;
    (0, vitest_1.expect)(negative.minValue).toBeNull;
    (0, vitest_1.expect)(nonpositive.minValue).toBeNull;
    (0, vitest_1.expect)(intNum.minValue).toBeNull;
    (0, vitest_1.expect)(multipleOfFive.minValue).toBeNull;
    (0, vitest_1.expect)(finite.minValue).toBeNull;
    (0, vitest_1.expect)(gtFive.minValue).toEqual(5);
    (0, vitest_1.expect)(gteFive.minValue).toEqual(5);
    (0, vitest_1.expect)(minFive.minValue).toEqual(5);
    (0, vitest_1.expect)(minFive.min(10).minValue).toEqual(10);
    (0, vitest_1.expect)(positive.minValue).toEqual(0);
    (0, vitest_1.expect)(nonnegative.minValue).toEqual(0);
    (0, vitest_1.expect)(safe.minValue).toEqual(Number.MIN_SAFE_INTEGER);
    (0, vitest_1.expect)(z.number().maxValue).toBeNull;
    (0, vitest_1.expect)(gtFive.maxValue).toBeNull;
    (0, vitest_1.expect)(gteFive.maxValue).toBeNull;
    (0, vitest_1.expect)(minFive.maxValue).toBeNull;
    (0, vitest_1.expect)(positive.maxValue).toBeNull;
    (0, vitest_1.expect)(nonnegative.maxValue).toBeNull;
    (0, vitest_1.expect)(intNum.minValue).toBeNull;
    (0, vitest_1.expect)(multipleOfFive.minValue).toBeNull;
    (0, vitest_1.expect)(finite.minValue).toBeNull;
    (0, vitest_1.expect)(ltFive.maxValue).toEqual(5);
    (0, vitest_1.expect)(lteFive.maxValue).toEqual(5);
    (0, vitest_1.expect)(maxFive.maxValue).toEqual(5);
    (0, vitest_1.expect)(maxFive.max(1).maxValue).toEqual(1);
    (0, vitest_1.expect)(negative.maxValue).toEqual(0);
    (0, vitest_1.expect)(nonpositive.maxValue).toEqual(0);
    (0, vitest_1.expect)(safe.maxValue).toEqual(Number.MAX_SAFE_INTEGER);
});
(0, vitest_1.test)("int getter", () => {
    (0, vitest_1.expect)(z.number().isInt).toEqual(false);
    (0, vitest_1.expect)(z.number().multipleOf(1.5).isInt).toEqual(false);
    (0, vitest_1.expect)(gtFive.isInt).toEqual(false);
    (0, vitest_1.expect)(gteFive.isInt).toEqual(false);
    (0, vitest_1.expect)(minFive.isInt).toEqual(false);
    (0, vitest_1.expect)(positive.isInt).toEqual(false);
    (0, vitest_1.expect)(nonnegative.isInt).toEqual(false);
    (0, vitest_1.expect)(finite.isInt).toEqual(false);
    (0, vitest_1.expect)(ltFive.isInt).toEqual(false);
    (0, vitest_1.expect)(lteFive.isInt).toEqual(false);
    (0, vitest_1.expect)(maxFive.isInt).toEqual(false);
    (0, vitest_1.expect)(negative.isInt).toEqual(false);
    (0, vitest_1.expect)(nonpositive.isInt).toEqual(false);
    (0, vitest_1.expect)(safe.isInt).toEqual(false);
    (0, vitest_1.expect)(intNum.isInt).toEqual(true);
    (0, vitest_1.expect)(multipleOfFive.isInt).toEqual(true);
});
(0, vitest_1.test)("finite getter", () => {
    (0, vitest_1.expect)(z.number().isFinite).toEqual(false);
    (0, vitest_1.expect)(gtFive.isFinite).toEqual(false);
    (0, vitest_1.expect)(gteFive.isFinite).toEqual(false);
    (0, vitest_1.expect)(minFive.isFinite).toEqual(false);
    (0, vitest_1.expect)(positive.isFinite).toEqual(false);
    (0, vitest_1.expect)(nonnegative.isFinite).toEqual(false);
    (0, vitest_1.expect)(ltFive.isFinite).toEqual(false);
    (0, vitest_1.expect)(lteFive.isFinite).toEqual(false);
    (0, vitest_1.expect)(maxFive.isFinite).toEqual(false);
    (0, vitest_1.expect)(negative.isFinite).toEqual(false);
    (0, vitest_1.expect)(nonpositive.isFinite).toEqual(false);
    (0, vitest_1.expect)(finite.isFinite).toEqual(true);
    (0, vitest_1.expect)(intNum.isFinite).toEqual(true);
    (0, vitest_1.expect)(multipleOfFive.isFinite).toEqual(true);
    (0, vitest_1.expect)(z.number().min(5).max(10).isFinite).toEqual(true);
    (0, vitest_1.expect)(safe.isFinite).toEqual(true);
});
